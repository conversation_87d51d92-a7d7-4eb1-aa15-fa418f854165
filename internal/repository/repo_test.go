package repository

import (
	"fmt"
	"hallsrv/internal/test"
	"testing"

	"git.keepfancy.xyz/back-end/frameworks/lib/random"
)

func TestActLogin(t *testing.T) {
	test.Init()
	ctx := test.NewCtx()
	data, err := GetContinuousLogin(ctx, 1)
	if err != nil {
		t.Fatalf("err:%+v", err)
	}

	fmt.Printf("data:%+v", data)
	data.Day = random.Int32n(4)
	if _, err := UpdateContinuousLogin(ctx, 1, data.Day); err != nil {
		t.Fatalf("err:%+v", err)
	}
}
