package repository

import (
	"context"
	"hallsrv/internal/dao/dao_continuous_login"
	"hallsrv/internal/model/model_continuous_login"
	"time"

	"git.keepfancy.xyz/back-end/frameworks/lib/timex"
	"github.com/sirupsen/logrus"
)

// 获取连续登录数据
func GetContinuousLogin(ctx context.Context, playerId uint64) (*model_continuous_login.TConstinuousLogin, error) {
	// 先查询缓存
	bean, err := dao_continuous_login.QueryRds(ctx, playerId)
	if err == nil {
		return bean, nil
	}

	// 缓存未命中,查询数据库
	bean, err = dao_continuous_login.QueryDb(ctx, playerId)
	if err != nil {
		return nil, err
	}

	// 回写缓存
	if err := dao_continuous_login.UpdateRds(ctx, bean); err != nil {
		// 缓存写入失败只记录日志,不影响主流程
		logrus.Errorf("update continuous login rds failed:%+v", err)
	}

	return bean, nil
}

func UpdateContinuousLogin(ctx context.Context, playerId uint64, day int32) (*model_continuous_login.TConstinuousLogin, error) {
	bean := &model_continuous_login.TConstinuousLogin{
		PlayerId: playerId,
		Day:      day,
		UpdateAt: timex.Now().Unix(),
	}

	// 第一次删除
	if err := dao_continuous_login.DeleteRds(ctx, playerId); err != nil {
		logrus.Errorf("delete continuous login rds failed:%+v", err)
		return nil, err
	}

	// 先更新数据库
	if err := dao_continuous_login.UpdateDb(ctx, bean); err != nil {
		return nil, err
	}

	// 删除缓存,让缓存失效
	if err := dao_continuous_login.DeleteRds(ctx, playerId); err != nil {
		// 缓存删除失败只记录日志,不影响主流程
		logrus.Errorf("delete continuous login rds failed:%+v", err)
		// 延迟双删,避免缓存不一致
		go func() {
			time.Sleep(time.Millisecond * 500)
			if err := dao_continuous_login.DeleteRds(context.Background(), playerId); err != nil {
				logrus.Errorf("delay delete continuous login rds failed:%+v", err)
			}
		}()
	}

	return bean, nil
}

func ClearContinuousLogin(ctx context.Context, playerId uint64) error {
	bean := &model_continuous_login.TConstinuousLogin{
		PlayerId: playerId,
		Day:      0,
		UpdateAt: 0,
	}

	// 先删除缓存
	if err := dao_continuous_login.DeleteRds(ctx, playerId); err != nil {
		logrus.Errorf("delete continuous login rds failed:%+v", err)
		return err
	}
	return dao_continuous_login.UpdateDb(ctx, bean)
}

func UpdateContinuousLoginByGmTs(ctx context.Context, playerId uint64, ts int64) error {
	bean, err := GetContinuousLogin(ctx, playerId)
	if err != nil {
		return err
	}

	// 先删除缓存
	if err := dao_continuous_login.DeleteRds(ctx, playerId); err != nil {
		logrus.Errorf("delete continuous login rds failed:%+v", err)
		return err
	}

	bean.UpdateAt = ts
	return dao_continuous_login.UpdateDb(ctx, bean)
}
