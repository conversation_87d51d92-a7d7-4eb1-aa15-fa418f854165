package services

import (
	"testing"

	test_init "hallsrv/internal/test"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	hallRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/hallrpc"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/recordx"
	"git.keepfancy.xyz/back-end/frameworks/kit/rpc/interceptor"
)

func TestHallService_OptPlayerItem(t *testing.T) {
	test_init.InitRedisConsul()
	recordx.Init()

	ctx := interceptor.NewRpcClientCtx(interceptor.WithProductId(1), interceptor.WithCountry("zh-cn"), interceptor.WithAppVersion("1.0.0"))

	rsp, err := GetHallServiceInstance().OptPlayerItem(ctx, &hallRpc.OptPlayerItemReq{
		PlayerId: 179,
		ItemList: []*commonPB.ItemBase{
			{ItemId: 101, ItemCount: 100},
			{ItemId: 102, ItemCount: 200},
		},
		ItemOperation: commonPB.ITEM_OPERATION_IO_ADD,
		ItemSource:    commonPB.ITEM_SOURCE_TYPE_IST_GM,
	})

	t.Logf(rsp.String(), err)
}
