package real_name

import (
	"context"
	"encoding/json"
	"errors"
	"hallsrv/internal/repo/real_name/office_auth"
	"hallsrv/internal/repo/real_name/real_name_def"
	"io"
	"net/http"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/frameworks/dict"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	"git.keepfancy.xyz/back-end/frameworks/lib/transform"
	"github.com/sirupsen/logrus"
)

type IRealNameAuthOfficeImpl struct{}

func (i *IRealNameAuthOfficeImpl) RealNameAuth(ctx context.Context, playerId uint64, name, idNum string) (*RealNameRet, error) {
	entry := logx.NewLogEntry(ctx)
	logEntry := entry.WithFields(logrus.Fields{
		dict.SysWordPlayerID: playerId,
		"idNum":              idNum,
	})
	logEntry.Tracef("RealNameAuth office - :%s", name)

	ret := &RealNameRet{}

	// 读取配置 不同版号，不同渠道都需要配置
	realNameCfg := cmodel.GetRealNameAuth(consul_config.WithGrpcCtx(ctx))
	appID := realNameCfg.Appid
	bizID := realNameCfg.Bizid
	key := realNameCfg.SecretKey

	fcmInstance, err := office_auth.NewFcmInstance(appID, bizID, key)
	if err != nil {
		logEntry.Errorf("new fcm instance err : %v", err)
		return ret, err
	}

	rspCheck, errCheck := fcmInstance.Check(&office_auth.Check{
		Ai:    transform.Uint642Str(playerId),
		Name:  name,
		IdNum: idNum,
	})

	if errCheck != nil || rspCheck.StatusCode != http.StatusOK {
		logEntry.Errorf("fcm check err : %v", errCheck)
		return ret, errCheck
	}

	if rspCheck.Body != nil {
		defer rspCheck.Body.Close()
	}

	// response body info
	body, _ := io.ReadAll(rspCheck.Body)

	info := office_auth.AuthIdentityRsp{}
	err = json.Unmarshal(body, &info)
	if err != nil {
		logEntry.Errorf("json unmarshal err : %v", err)
		return nil, err
	}

	if info.ErrCode != 0 {
		logEntry.Errorf("rsp err : %s", info.ErrMsg)
		return ret, errors.New(info.ErrMsg)
	}

	Pi := ""
	if info.Data != nil && info.Data.CheckResult != nil {
		checkRet := info.Data.CheckResult
		switch checkRet.State {
		case office_auth.AuthStatusSuccess:
			Pi = checkRet.Pi
			ret.State = real_name_def.AuthRetAgreed
		case office_auth.AuthStatusUnderway:
			checkQueryRsp, errQuery := queryOfficial(ctx, playerId)
			if errQuery != nil {
				logEntry.WithError(errQuery).Error("real-name auth office check fail")
				ret.State = real_name_def.AuthRetDisagree
			}

			if checkQueryRsp != nil && checkQueryRsp.Data != nil && checkQueryRsp.Data.CheckResult != nil {
				queryRet := checkQueryRsp.Data.CheckResult
				if queryRet.State == 0 {
					Pi = queryRet.Pi
					ret.State = real_name_def.AuthRetAgreed
				} else {
					ret.State = real_name_def.AuthRetDisagree
				}
			}
		case office_auth.AuthStatusFail:
			ret.State = real_name_def.AuthRetDisagree
		}
	}
	ret.Pi = Pi

	return ret, nil
}

func queryOfficial(ctx context.Context, playerId uint64) (*office_auth.AuthIdentityRsp, error) {
	entry := logx.NewLogEntry(ctx)
	logEntry := entry.WithFields(logrus.Fields{
		dict.SysWordPlayerID: playerId,
	})
	logEntry.Tracef("queryOfficial in : %d", playerId)

	// 读取配置 不同版号，不同渠道都需要配置
	realNameCfg := cmodel.GetRealNameAuth(consul_config.WithGrpcCtx(ctx))
	appID := realNameCfg.Appid
	bizID := realNameCfg.Bizid
	key := realNameCfg.SecretKey

	fcmInstance, err := office_auth.NewFcmInstance(appID, bizID, key)
	if err != nil {
		logEntry.Errorf("new fcm instance err : %v", err)
		return nil, err
	}

	rspCheck, errCheck := fcmInstance.Query(&office_auth.Query{
		Ai: transform.Uint642Str(playerId),
	})

	if errCheck != nil || rspCheck.StatusCode != http.StatusOK {
		logEntry.Errorf("fcm query err : %v", errCheck)
		return nil, errCheck
	}

	if rspCheck.Body != nil {
		defer rspCheck.Body.Close()
	}

	// response body info
	body, _ := io.ReadAll(rspCheck.Body)
	info := office_auth.AuthIdentityRsp{}
	err = json.Unmarshal(body, &info)
	if err != nil {
		logEntry.Errorf("json unmarshal err : %v", err)
		return nil, err
	}

	if info.ErrCode != 0 {
		logEntry.Errorf("rsp err : %s", info.ErrMsg)
		return nil, errors.New(info.ErrMsg)
	}
	return &info, nil
}
