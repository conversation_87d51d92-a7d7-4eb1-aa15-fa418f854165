package real_name

import "context"

type RealNameType int

const (
	RealNameTypeOffice RealNameType = 1 // 中宣部官方API
	RealNameTypeShuMai RealNameType = 2 // 阿里云速脉API
)

type RealNameRet struct {
	State int    `json:"status"` // 认证结果 0：认证成功，1：认证中，2：认证失败
	Pi    string `json:"pi"`     // 已通过实名认证的用户标识，已认证通过用户必填
}

// INameAuth 实名认证接口
type INameAuth interface {
	RealNameAuth(ctx context.Context, playerId uint64, name, idNum string) (*RealNameRet, error)
}

func DoRealNameAuth(ctx context.Context, rnType RealNameType, playerId uint64, name, idNum string) (*RealNameRet, error) {
	var iNameAuth INameAuth
	switch rnType {
	case RealNameTypeOffice:
		iNameAuth = &IRealNameAuthOfficeImpl{}
	case RealNameTypeShuMai:
		iNameAuth = &IRealNameAuthShuMaiImpl{}
	}
	return iNameAuth.RealNameAuth(ctx, playerId, name, idNum)
}
