package shumai_auth

import (
	"context"
	"fmt"
	test_init "hallsrv/internal/test"
	"testing"

	"git.keepfancy.xyz/back-end/frameworks/dict"
	"git.keepfancy.xyz/back-end/frameworks/lib/httpx"
	"github.com/spf13/viper"
)

func TestPostShuMai(t *testing.T) {
	viper.Set(dict.ConfigConsulAddr, "************:8500")

	shuMaiRet, err := ShuMaiRealNameAuth(context.TODO(),"张三", "******************", "13482885319")
	if err != nil {
		t.Error(err)
		return
	}
	t.Logf("%+v", shuMaiRet)
}

func TestDemo(t *testing.T) {
	url := "https://mobile3elements.shumaidata.com/mobile/verify_real_name"
	appCode := "1dc836b7ae2349498a93bca52bfa3b51"

	params := map[string]string{
		"idcard": "4211****182416",
		"mobile": "188****2416",
		"name":   "张*",
	}

	// 设置请求头
	headers := make(map[string]string)
	headers["Content-Type"] = "application/x-www-form-urlencoded"
	headers["Authorization"] = "APPCODE " + appCode

	result, err := httpx.PostBackFormByHeader(url, headers, params)
	if err != nil {
		fmt.Println("请求失败:", err)
		return
	}
	t.Log(result)
}

func TestShuMaiHoliday(t *testing.T) {
	host := "https://jrxxcxsmkj.market.alicloudapi.com/holiday/search"
	appcode := "1dc836b7ae2349498a93bca52bfa3b51"

	headers := map[string]string{
		"Authorization": "APPCODE " + appcode,
	}

	queryArray := map[string]string{
		"day": "20241001",
	}

	result, err := httpx.GetBackFormByHeader(host, headers, queryArray)
	if err != nil {
		fmt.Println("请求失败:", err)
		return
	}

	t.Logf("响应:%v", string(result))
}

func TestQueryCurHolidayType(t *testing.T) {
	test_init.InitRedisConsul()
	holidayType, err:= QueryCurHolidayType(context.Background())
	if err != nil {
		t.Error(err)
		return
	}

	t.Logf("holidayType:%v", holidayType)
}
