package office_auth

import (
	"encoding/json"
	"io"
	"testing"
)

func TestFcm_Check(t *testing.T) {
	fcm, _ := NewFcmInstance("478c7664685847468d8e9a5ba38308e2", "1101999999", "601f3a3ef7dae2f9ef6f4932d7ef8f72")
	rsp, err := fcm.Check(&Check{
		Ai:    "111111",
		Name:  "尹艳龙",
		IdNum: "430488198901023345",
	})

	if err != nil {
		t.<PERSON><PERSON><PERSON>("fcm check err : %v", err)
	}

	t.Log(rsp)

	body, _ := io.ReadAll(rsp.Body)
	info := AuthIdentityRsp{}
	err = json.Unmarshal(body, &info)
	if err != nil {
		t.Error(err)
	}
	if info.ErrCode != 0 {
		t.Log(info.ErrMsg)
	} else {
		t.Log(info.Data)
	}
}
