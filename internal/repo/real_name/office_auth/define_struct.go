package office_auth

const (
	AuthStatusSuccess  = 0 // 认证成功
	AuthStatusUnderway = 1 // 认证中
	AuthStatusFail     = 2 // 认证失败
)

// Check data
type Check struct {
	Ai    string `json:"ai"`
	Name  string `json:"name"`
	IdNum string `json:"idNum"`
}

// Query data
type Query struct {
	Ai string `json:"ai"`
}

// Behavior login or logout
type Behavior struct {
	No int    `json:"no"` // 在批量模式中标识一条行为数据，取值1～128
	Si string `json:"si"` // 一个会话标识
	Bt int    `json:"bt"` // 游戏用户行为：0-下线，1-上线
	Ot int64  `json:"ot"` // 行为发生时间，单位为秒
	Ct int    `json:"ct"` // 用户行为上报类型，0-已认证用户，1-游客用户
	Di string `json:"di"` // 设备标识，游客模式下必填
	Pi string `json:"pi"` // 已通过实名认证的用户标识，已认证通过用户必填
}

// Collections behaviors
type Collections struct {
	Collections *[]Behavior `json:"collections"`
}

// Status response status
type Status struct {
	ErrCode int    `json:"errcode"`
	ErrMsg  string `json:"errmsg"`
}

type AuthResult struct {
	State int    `json:"status"` // 认证结果 0：认证成功，1：认证中，2：认证失败
	Pi    string `json:"pi"`     // 已通过实名认证的用户标识
}

type AuthData struct {
	CheckResult *AuthResult `json:"result"`
}

// AuthIdentityRsp 实名认证结果
type AuthIdentityRsp struct {
	ErrCode int       `json:"errcode"`
	ErrMsg  string    `json:"errmsg"`
	Data    *AuthData `json:"data"`
}
