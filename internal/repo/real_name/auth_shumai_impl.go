package real_name

import (
	"context"
	"errors"
	"hallsrv/internal/repo/real_name/real_name_def"
	"hallsrv/internal/repo/real_name/shumai_auth"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/frameworks/dict"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	"github.com/sirupsen/logrus"
)

type IRealNameAuthShuMaiImpl struct{}

func (i *IRealNameAuthShuMaiImpl) RealNameAuth(ctx context.Context, playerId uint64, name, idNum string) (*RealNameRet, error) {
	entry := logx.NewLogEntry(ctx)
	logEntry := entry.WithFields(logrus.Fields{
		dict.SysWordPlayerID: playerId,
		"idNum":              idNum,
	})
	logEntry.Tracef("RealNameAuth shumai - :%s", name)

	ret := &RealNameRet{}

	realNameCfg := cmodel.GetRealNameAuth(consul_config.WithGrpcCtx(ctx))
	openAuth := realNameCfg.Enable

	if !openAuth {
		return nil, errors.New("real-name auth not open")
	}

	// 调用身份证实名认证接口
	shuMaiRet, err := shumai_auth.ShuMaiRealNameAuth(ctx, name, idNum, "")
	if err != nil {
		logEntry.Errorf("real-name check [shumai] fail err : %v", err)
		return ret, err
	}

	if shuMaiRet.Code != "0" {
		logEntry.Errorf("rsp err : %s", shuMaiRet.Message)
		return ret, errors.New(shuMaiRet.Message)
	}

	//验证结果   1:一致  2:不一致  3:无记录  -1:异常
	switch shuMaiRet.Result.Res {
	case shumai_auth.AuthStatusAbnormal:
		ret.State = real_name_def.AuthRetAbnormal
	case shumai_auth.AuthStatusAgree:
		ret.State = real_name_def.AuthRetAgreed
	case shumai_auth.AuthStatusDisagree:
		ret.State = real_name_def.AuthRetDisagree
	case shumai_auth.AuthStatusNoRecord:
		ret.State = real_name_def.AuthRetNoRecord
	}

	return ret, nil
}
