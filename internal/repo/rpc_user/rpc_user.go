package rpcUser

import (
	"context"
	"errors"
	"fmt"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	userRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/userrpc"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/rpc_biz/crpc_user"
)

// RpcRealNameAuthQuery 查询实名认证
func RpcRealNameAuthQuery(ctx context.Context, productId int32, playerId uint64) (bool, error) {
	entry := logx.NewLogEntry(ctx)
	data, err := crpc_user.RpcRealNameAuthQuery(ctx, productId, playerId)
	if err != nil {
		entry.Errorf("RpcRealNameAuthQuery fail : %v", err)
		return false, err
	}
	if data.GetRet().Code != commonPB.ErrCode_ERR_SUCCESS {
		return false, errors.New(data.GetRet().Desc)
	}
	return data.IsRealName, nil
}

// RpcUpdateRealNameAuth 更新实名认证状态
func RpcUpdateRealNameAuth(ctx context.Context, auth *commonPB.PlayerRealNameAuth) error {
	entry := logx.NewLogEntry(ctx)
	req := &userRpc.RealNameAuthReq{
		AuthInfo: auth,
	}
	data, err := crpc_user.RpcRealNameAuthUpdate(ctx, req)
	if err != nil {
		entry.Errorf("RpcRealNameAuthQuery fail : %v", err)
		return err
	}
	if data.GetRet().Code != commonPB.ErrCode_ERR_SUCCESS {
		return errors.New(data.GetRet().Desc)
	}
	return nil
}

// RpcQueryPlayerAgeInfo 查询玩家年龄信息
func RpcQueryPlayerAgeInfo(ctx context.Context, productId int32, playerId uint64) (commonPB.USER_AGE, error) {
	entry := logx.NewLogEntry(ctx)
	if productId == 0 || playerId <= 0 {
		return commonPB.USER_AGE_UA_UNKNOWN, fmt.Errorf("product id or playerId info is empty")
	}

	rpcReq := &userRpc.PlayerAgeQueryReq{
		ProductId: productId,
		PlayerId:  playerId,
	}

	rsp, err := crpc_user.RpcQueryPlayerAgeInfo(ctx, rpcReq)
	if err != nil || rsp == nil {
		entry.Errorf("rpc query player:%d age failed : %v", playerId, err)
		return commonPB.USER_AGE_UA_UNKNOWN, fmt.Errorf("rpc query player age failed : %v", err)
	}

	entry.Debugf("rpc query player:%d age success : %v", playerId, rsp)

	return rsp.GetAge(), nil
}

// RpcQueryPlayerExtendInfo 查询玩家扩展信息
func RpcQueryPlayerExtendInfo(ctx context.Context, productId int32, playerId uint64) (*commonPB.ExtendUserInfo, error) {
	entry := logx.NewLogEntry(ctx)
	
	rpcReq := &userRpc.QueryPlayerExtendInfoReq{
		ProductId: productId,
		PlayerId:  playerId,
	}

	extendInfo, err := crpc_user.RpcQueryPlayerExtendInfo(ctx, rpcReq)
	if err != nil {
		entry.Errorf("rpc query player:%d extend info failed : %+v", playerId, err)
		return nil, fmt.Errorf("rpc query player extend info failed : %+v", err)
	}

	entry.Debugf("rpc query player:%d extend info success : %+v", playerId, extendInfo)

	return extendInfo, nil
}

// RpcUpdateGuideProgress 更新玩家新手引导进度
func RpcUpdateGuideProgress(ctx context.Context, productId int32, playerId uint64, progress int32) error {
	entry := logx.NewLogEntry(ctx)
	
	rpcReq := &userRpc.UpdatePlayerExtendInfoReq{
		ProductId: productId,
		PlayerId:  playerId,
		ExtendInfo: &commonPB.ExtendUserInfo{
			NoviceGuide: progress,
		},
	}

	rsp, err := crpc_user.RpcUpdatePlayerExtendInfo(ctx, rpcReq)
	if err != nil {
		entry.Errorf("rpc update player:%d extend info failed : %+v", playerId, err)
		return fmt.Errorf("rpc update player extend info failed : %+v", err)
	}

	entry.Debugf("rpc update player:%d extend info success : %+v", playerId, rsp)

	return nil
}
