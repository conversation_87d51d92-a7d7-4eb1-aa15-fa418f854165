package record

import (
	"context"
	"encoding/json"
	"errors"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/analyze/a_hall"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/recordx"
	"git.keepfancy.xyz/back-end/frameworks/kit/kafka/event"
	"git.keepfancy.xyz/back-end/frameworks/kit/safego"
	"github.com/sirupsen/logrus"
	"github.com/spf13/cast"
	"log"
)

var (
	// ck
	rItemSender event.Sender
	// 数数
	teItemSender event.Sender
)

// Init 初始化发送者
func Init() {
	// 配置列表封装初始化参数
	senderConfigs := []struct {
		senderPtr *event.Sender // 接收初始化结果的指针
		topicName string
	}{
		// 数数事件配置
		{&rItemSender, new(RItem).GetTableName()},
		// 登录事件配置
		{&teItemSender, new(a_hall.TeItemInfo).GetEvent()},
	}

	// 统一初始化逻辑
	for _, conf := range senderConfigs {
		sender, err := event.NewKafkaSender(event.WithTopic(conf.topicName))
		if err != nil {
			log.Fatalf("创建 Kafka 发送者失败 [%s]: %v", conf.topicName, err)
		}
		*conf.senderPtr = sender
	}

}

var (
	errCallServiceFailed = errors.New("调用服务失败")
)

type LogAgencyRecord struct {
}

func NewLogAgencyInstance() *LogAgencyRecord {
	return &LogAgencyRecord{}
}

// ItemRecord 道具流水
func (l *LogAgencyRecord) ItemRecord(record *ItemRecordParam) {
	entry := logx.NewLogEntry(record.Ctx)
	entry.Debugf("add item record, player:%d, RewardInfo:%s", record.PlayerId, record.RewardInfo.String())
	if record.RewardInfo == nil {
		entry.Errorf("add item record error: RewardInfo is nil, player:%d", record.PlayerId)
		return
	}

	header := recordx.NewDefaultHeaderFromCtx(record.Ctx)
	claimID := record.RewardInfo.GetClaimID()
	sourceType := int32(record.RewardInfo.GetSourceType())

	itemList := record.RewardInfo.GetItemList()
	for _, itemInfo := range itemList {
		if itemInfo.GetItem() == nil {
			entry.Errorf("add item record error: item is nil, player:%d, RewardInfo:%s", record.PlayerId, record.RewardInfo.String())
			continue
		}

		repObj := &RItem{
			PlayerId:     record.PlayerId,
			ClaimId:      claimID,
			InstanceId:   itemInfo.GetItem().GetInstanceId(),
			ItemId:       itemInfo.GetItem().GetItemId(),
			ItemType:     int32(itemInfo.GetItem().GetItemType()),
			OptType:      record.OptType,
			SrcType:      sourceType,
			CurCount:     itemInfo.GetItemCount(),
			DeltaCount:   itemInfo.GetItemDeltaCount(),
			originalData: record.OriginalData,
		}

		body := recordx.SerializeData(header, repObj)
		err := rItemSender.Send(context.Background(), event.NewMessage(cast.ToString(record.PlayerId), []byte(body)))
		if err != nil {
			logrus.Errorf("r item send failed err=%s te= %+v", err, body)
		}

	}
	entry.Debugf("add item record success, player:%d, RewardInfo:%s", record.PlayerId, record.RewardInfo.String())
}

func LogItemRecord(ctx context.Context, record *a_hall.TeItemInfo) {

	logrus.Debugf("ItemRecord In uid:%d - product_id%d", record.PlayerId, record.ProductID)

	safego.Go(func() {
		sendItemTeEvent(record)
	})
}

func sendItemTeEvent(record *a_hall.TeItemInfo) {
	// 数数上报
	teBody, err := json.Marshal(record)
	if err != nil {
		logrus.Errorf("te item marshal failed err=%s te= %+v", err, record)
		return
	}

	err = teItemSender.Send(context.Background(), event.NewMessage(cast.ToString(record.PlayerId), teBody))
	if err != nil {
		logrus.Errorf("te item send failed err=%s te= %s", err, string(teBody))
	}
}
