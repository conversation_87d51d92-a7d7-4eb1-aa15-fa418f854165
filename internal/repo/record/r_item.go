package record

import (
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/recordx"
	"git.keepfancy.xyz/back-end/frameworks/lib/transform"
)

// RItem 道具流水
type RItem struct {
	PlayerId     uint64 // 玩家id
	ClaimId      string // 领奖id
	InstanceId   string // 实例id
	ItemId       int64  // 道具id
	ItemType     int32  // 道具类型 commPb.ITEM_TYPE
	OptType      int32  // 操作类型 commPb.ITEM_OPERATION
	SrcType      int32  // 操作来源 commPb.ITEM_SOURCE_TYPE
	CurCount     int64  // 当前数量
	DeltaCount   int64  // 变化数量
	originalData string // 元素数据(json)
}

func (r *RItem) GetTableName() string {
	return "r_item"
}

func (r *RItem) Format() string {
	return recordx.MarshalWithLine(
		transform.Uint642Str(r.PlayerId),
		r.<PERSON>,
		r.InstanceId,
		transform.Int642Str(int64(r.ItemId)),
		transform.Int2Str(int(r.ItemType)),
		transform.Int2Str(int(r.OptType)),
		transform.Int2Str(int(r.SrcType)),
		transform.Int642Str(r.CurCount),
		transform.Int642Str(r.DeltaCount),

		r.originalData,
	)
}
