package record

import (
	"github.com/panjf2000/ants"
	"github.com/sirupsen/logrus"
)

var DefaultLogging *LogWorkPool

const workNums = 100

type LogWorkPool struct {
	workerPoolSize int
	workerItemPool *ants.PoolWithFunc

	loggAgency *LogAgencyRecord
}

func NewHallRecordWorkPool() *LogWorkPool {
	return &LogWorkPool{
		workerPoolSize: workNums,
		loggAgency:     NewLogAgencyInstance(),
	}
}

// PubItemRecord 发布道具流水日志记录任务
func (l *LogWorkPool) PubItemRecord(record *ItemRecordParam) {
	if err := l.workerItemPool.Invoke(record); err != nil {
		logrus.Errorf("publish item record error: %v", err)
	}
}

// StartItemRecordWorkerPool 开启道具流水日志作业Pool
func (l *LogWorkPool) StartItemRecordWorkerPool() {
	l.workerItemPool, _ = ants.NewPoolWithFunc(l.workerPoolSize, func(i interface{}) {
		l.loggAgency.ItemRecord(i.(*ItemRecordParam))
	})
}
