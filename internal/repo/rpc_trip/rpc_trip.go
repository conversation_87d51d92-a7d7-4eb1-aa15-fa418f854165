package rpcTrip

import (
	"context"
	"fmt"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	tripRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/triprpc"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/rpc_biz/crpc_trip"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/rpc_biz/crpc_user"
	"git.keepfancy.xyz/back-end/frameworks/kit/rpc/interceptor"
)

// RpcJoinTrip RPC到TripSrv查询进入房间id
func RpcJoinTrip(ctx context.Context, playerId uint64, pondId int64, gameType commonPB.GAME_TYPE, roomType commonPB.ROOM_TYPE) (*commonPB.RoomInfo, error) {
	entry := logx.NewLogEntry(ctx)
	productId := interceptor.GetRPCOptions(ctx).ProductId

	// 先查询玩家信息 user服
	briefInfo, err := crpc_user.RpcGetPlayerInfo(ctx, productId, playerId)
	if err != nil {
		entry.Errorf("query player:%d, info err:%+v", playerId, err)
		return nil, err
	}

	// 查询trip服务
	tripRpcCli := crpc_trip.GetTripRpcInstance().GetTripRpcClient()
	if tripRpcCli == nil {
		entry.Errorf("player:%d join pond:%d get trip cli nil", playerId, pondId)
		return nil, fmt.Errorf("trip rpc client is nil")
	}

	fisher := &commonPB.Fisher{
		BriefUserInfo: briefInfo.RichUserInfo.BriefUserInfo,
	}

	var fishers []*commonPB.Fisher
	fishers = append(fishers, fisher)

	rsp, err := tripRpcCli.JoinTripRoom(ctx, &tripRpc.JoinTripReq{
		PondId:    pondId,
		GameType:  gameType,
		Fishers:   fishers,
		ProductId: interceptor.GetRPCOptions(ctx).ProductId,
	})

	if err != nil || rsp == nil || len(rsp.GetJoinInfos()) == 0 {
		entry.WithError(err).Errorf("rpc trip failed, player:%d, pondId:%d, gameType:%d, roomType:%d, joinLen:%d", playerId, pondId, gameType, roomType, len(rsp.JoinInfos))
		return nil, nil
	}

	entry.Infof("join trip player:%d, pondId:%d, gameType:%d, roomType:%d, roomInfo:%s", playerId, pondId, gameType, roomType, rsp.JoinInfos[0].GetRoomInfo())

	if rsp.JoinInfos[0].GetRoomInfo() == nil {
		return nil, fmt.Errorf("room info is nil")
	}

	return rsp.JoinInfos[0].GetRoomInfo(), nil
}

// RpcGetPlayerRoomInfo 查询玩家房间信息
func RpcGetPlayerRoomInfo(ctx context.Context, playerId uint64) (*commonPB.RoomInfo, error) {
	entry := logx.NewLogEntry(ctx)
	// 先查询玩家信息 trip服务
	rpcRsp, err := crpc_trip.RpcGetPlayerRoomInfo(ctx, playerId)
	if err != nil || rpcRsp.GetRoomInfo() == nil {
		entry.Warnf("rpc RpcGetPlayerRoomInfo player:%d, roomInfo:%+v, warn: %+v", playerId, rpcRsp.GetRoomInfo(), err)
		return nil, err
	}

	return rpcRsp.GetRoomInfo(), nil
}
