package modelPlayer

import (
	"time"

	"git.keepfancy.xyz/back-end/frameworks/lib/transform"
	"github.com/sirupsen/logrus"
)

const TablePlayerState = "t_player_state"

// PlayerStateBm 结构体与数据库表 t_player_state 对应
type PlayerStateBm struct {
	PlayerId   uint64    `xorm:"'player_id' notnull pk autoincr" json:"player_id"`
	GoodsBuy   string    `xorm:"'goods_buy' notnull text" json:"goods_buy"`   // 限制单次购买商品 bitmap
	ExpLv      string    `xorm:"'exp_lv' notnull default ''" json:"exp_lv"`   // 经验等级奖励 bitmap
	RedDot     string    `xorm:"'red_dot' notnull default ''" json:"red_dot"` // 红点 bitmap
	UpdateTime time.Time `xorm:"'update_time' notnull default CURRENT_TIMESTAMP updated" json:"update_time"`
}

func (p *PlayerStateBm) TableName() string {
	return TablePlayerState
}

func NewPlayerStateBmFromRdsHash(rdsHash map[string]string) *PlayerStateBm {
	if rdsHash == nil {
		return nil
	}

	playerStateBm := &PlayerStateBm{}
	err := transform.Map2Struct(rdsHash, playerStateBm)

	if err != nil {
		logrus.Errorf("transform.Map2Struct err:%v", err)
		return nil
	}

	return playerStateBm
}

// ToRedisHashField 转化为redis hash field
func (p *PlayerStateBm) ToRedisHashField() map[string]interface{} {
	if p == nil {
		return nil
	}

	hashMap := make(map[string]interface{})
	err := transform.Struct2Map(p, hashMap)
	if err != nil {
		logrus.Errorf("transform.Struct2Map err:%v", err)
		return nil
	}

	return hashMap
}

// GetPlayerStateBmGoodsRdsField 获取商品购买限制 bitmap redis field
func GetPlayerStateBmGoodsRdsField() string {
	return "goods_buy"
}

// GetPlayerStateBmExpLvRdsField 获取玩家等级 bitmap redis field
func GetPlayerStateBmExpLvRdsField() string {
	return "exp_lv"
}

// GetPlayerStateBmRedDotRdsField 获取玩家红点 bitmap redis field
func GetPlayerStateBmRedDotRdsField() string {
	return "red_dot"
}
