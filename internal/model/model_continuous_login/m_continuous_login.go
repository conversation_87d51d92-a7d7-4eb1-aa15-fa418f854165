package model_continuous_login

import "git.keepfancy.xyz/back-end/frameworks/lib/timex"

const TableConstinuousLogin = "t_continuous_login"

// 连续登录
type TConstinuousLogin struct {
	PlayerId uint64 `xorm:"pk comment('玩家ID')" json:"player_id"`
	Day      int32  `xorm:"comment('领取奖励日数')" json:"day"`
	UpdateAt int64  `xorm:"comment('更新时间')" json:"update_at"`
}

func New(playerId uint64) *TConstinuousLogin {
	return &TConstinuousLogin{
		PlayerId: playerId,
		UpdateAt: timex.Now().Unix(),
	}
}

func (t TConstinuousLogin) TableName() string {
	return TableConstinuousLogin
}

func (t *TConstinuousLogin) Serialize() string {
	return ""
}

func (t *TConstinuousLogin) Deserialize() string {
	return ""
}
