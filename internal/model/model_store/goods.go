package modelStore

import (
	"context"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
)

type GoodsInfo struct {
	GoodsId   int64              `json:"goods_id"`   // 商品ID
	CostItem  int64              `json:"cost_item"`  // 成本商品id
	CostCount int64              `json:"cost_count"` // 成本数量
	GoodsConf *cmodel.GoodsBasic `json:"goods_conf"` // 商品配置信息
}

// NewGoodsInfo 创建GoodsInfo实例
func NewGoodsInfo(ctx context.Context, storeBuyId int64, storeStyle commonPB.STORE_SHOW_STYLE) (*GoodsInfo, error) {
	var goodsInfo GoodsInfo

	if storeStyle == commonPB.STORE_SHOW_STYLE_STRT_ROOM {
		// 这里是房间商城的配置
		storeRoomConf := cmodel.GetStoreRoom(storeBuyId, consul_config.WithGrpcCtx(ctx))
		if storeRoomConf == nil {
			return nil, protox.CodeError(commonPB.ErrCode_ERR_CONF_ERROR, "storeRoom")
		}

		goodsInfo.GoodsId = storeRoomConf.GoodsId
		goodsInfo.CostItem = storeRoomConf.CostItem
		goodsInfo.CostCount = storeRoomConf.CostCount
	} else {
		// 商城购买配置
		storeBuyConf := cmodel.GetStoreBuy(storeBuyId, consul_config.WithGrpcCtx(ctx))
		if storeBuyConf == nil {
			return nil, protox.CodeError(commonPB.ErrCode_ERR_CONF_ERROR, "storeBuy")
		}

		goodsInfo.GoodsId = storeBuyConf.GoodsId
		goodsInfo.CostItem = storeBuyConf.CostItem
		goodsInfo.CostCount = storeBuyConf.CostCount
	}

	// 查询商品信息
	goodsConf := cmodel.GetGoodsBasic(goodsInfo.GoodsId, consul_config.WithGrpcCtx(ctx))
	if goodsConf == nil {
		return nil, protox.CodeError(commonPB.ErrCode_ERR_CONF_ERROR, "goodsBasic")
	}

	goodsInfo.GoodsConf = goodsConf

	return &goodsInfo, nil
}
