package modelStore

import (
	"encoding/json"
	"time"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/frameworks/lib/timex"
	"github.com/sirupsen/logrus"
)

type GoodsBuyInfo struct {
	GoodsId     int64 `json:"goods_id"`      // 商品ID
	BuyTimes    int32 `json:"buy_times"`     // 购买次数
	LastBuyTime int64 `json:"last_buy_time"` // 最后购买时间(时间戳)
}

// NewGoodsBuyInfoJson 从json初始化
func NewGoodsBuyInfoJson(jsonStr string) *GoodsBuyInfo {
	tempGoodsBuyInfo := &GoodsBuyInfo{}
	err := json.Unmarshal([]byte(jsonStr), tempGoodsBuyInfo)
	if err != nil {
		logrus.Warnf("init goods buy info, json:%s, error:%v", jsonStr, err)
		return nil
	}

	return tempGoodsBuyInfo
}

// NewGoodsBuyInfo 从goodsId初始化
func NewGoodsBuyInfo(goodsId int64) *GoodsBuyInfo {
	return &GoodsBuyInfo{
		GoodsId:     goodsId,
		BuyTimes:    0,
		LastBuyTime: 0,
	}
}

// ToJsonStr 转化为json字符串
func (g *GoodsBuyInfo) ToJsonStr() string {
	jsonStr, err := json.Marshal(g)
	if err != nil {
		return ""
	}

	return string(jsonStr)
}

// ToProto 转化为proto
func (g *GoodsBuyInfo) ToProto() *commonPB.GoodsBuyInfo {
	if g == nil {
		return nil
	}

	return &commonPB.GoodsBuyInfo{
		GoodsId:     g.GoodsId,
		BuyTimes:    g.BuyTimes,
		LastBuyTime: g.LastBuyTime,
	}
}

// IsValid 判断是否有效
func (g *GoodsBuyInfo) IsValid() bool {
	if g == nil || g.GoodsId <= 0 {
		return false
	}
	return true
}

// CalTimes 根据限制类型计算次数(超过限制时间则清零)
func (g *GoodsBuyInfo) CalTimes(limitType int32) {
	if !g.IsValid() || limitType <= 0 {
		return
	}

	nowTime := timex.Now()
	switch limitType {
	case int32(commonPB.BUY_LIMIT_TYPE_BLT_DAY):
		if !timex.IsSameDay(time.Unix(g.LastBuyTime, 0), nowTime) {
			g.BuyTimes = 0
		}
	case int32(commonPB.BUY_LIMIT_TYPE_BLT_WEEK):
		if !timex.IsSameWeek(time.Unix(g.LastBuyTime, 0), nowTime) {
			g.BuyTimes = 0
		}
	case int32(commonPB.BUY_LIMIT_TYPE_BLT_MONTH):
		if !timex.IsSameMonth(time.Unix(g.LastBuyTime, 0), nowTime) {
			g.BuyTimes = 0
		}
	}
}

// TODO 这里是玩家状态信息表 使用bitmap存储 例如 是否购买了某个商品 是否买了注册送等
type PlayerBitMap struct {
	PlayerId int64  `json:"player_id"` // 玩家ID
	GoodsBuy string `json:"goods_buy"` // 购买商品信息 (bitmap)
}

// ToRedisHash 转化为redis hash
func (p *PlayerBitMap) ToRedisHash() map[string]string {
	if p == nil {
		return nil
	}

	return map[string]string{
		"goods_buy": p.GoodsBuy,
	}
}
