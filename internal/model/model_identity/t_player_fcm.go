package model

import "time"

const TableTPlayFcm = "t_player_real_name_auth"

type TPlayerRealNameAuth struct {
	ProductId  int32     `xorm:"pk not null comment('产品ID') int(10)"`
	PlayerId   uint64    `xorm:"pk not null comment('玩家ID') BIGINT(20)"`
	Pi         string    `xorm:"not null default '' comment('实名认证返回pi')"`
	RealName   string    `xorm:"not null default '' comment('真实姓名')"`
	IdCardNum  string    `xorm:"not null default '' comment('身份证号码')"`
	Year       int       `xorm:"not null default 0 comment('年')"`
	Month      int       `xorm:"not null default 0 comment('月')"`
	Day        int       `xorm:"not null default 0 comment('日')"`
	CreateTime time.Time `xorm:"not null comment('创建时间') DATETIME created"`
}
