package model_pond_store

import (
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
)

// 用户购买记录
type PondStoreBuy struct {
	PondId int64                      `json:"pond_id"` // 钓场id
	Bought map[int64]PlayerPondBought `json:"bought"`  // 场次id:购买记录
}

type PlayerPondBought struct {
	BatchId int64           `json:"batch_id"` // 场次id
	Bought  map[int64]int32 `json:"bought"`   // 商品id:购买次数
	TsEnd   int64           `json:"ts_end"`   // 结束时间
	TsStart int64           `json:"ts_start"` // 开始时间
}

func NewPondStoreBuy(pondId int64) *PondStoreBuy {
	return &PondStoreBuy{
		PondId: pondId,
		Bought: make(map[int64]PlayerPondBought),
	}
}

// 商品道具结构
type PoodGood struct {
	GoodsId  int64 `json:"goods_id"`  // 商品id
	Nums     int32 `json:"nums"`      // 可购买数量
	Discount int32 `json:"discount"`  // 折扣
	Price    int32 `json:"price"`     // 原价
	CostItem int64 `json:"cost_item"` // 价格类型
}

// 商品场次结构
type PondBatch struct {
	BatchId   int64       `json:"batch_id"`   // 场次id
	TsStart   int64       `json:"ts_start"`   // 开始时间
	TsEnd     int64       `json:"ts_end"`     // 结束时间
	PondGoods []*PoodGood `json:"pond_goods"` // 道具列表
}

// 钓场商城数据结构
type TPondStore struct {
	PondId      int64                `json:"pond_id"`      // 钓场id
	PondBatches map[int64]*PondBatch `json:"pond_batches"` // 商品场次信息
}

func NewPondStore(pondId int64) *TPondStore {
	return &TPondStore{
		PondId:      pondId,
		PondBatches: make(map[int64]*PondBatch),
	}
}

func (t *PoodGood) ToProto() *commonPB.PondGoods {
	rtn := &commonPB.PondGoods{
		GoodId:    t.GoodsId,
		Nums:      t.Nums,
		Discount:  t.Discount,
		CostItem:  t.CostItem,
		CostCount: int64(t.Price),
	}
	return rtn
}

func (t *PondBatch) ToProto() *commonPB.PondBatch {
	rtn := &commonPB.PondBatch{
		BatchId: t.BatchId,
		TsStart: t.TsStart,
		TsEnd:   t.TsEnd,
	}
	for _, goods := range t.PondGoods {
		rtn.List = append(rtn.List, goods.ToProto())
	}
	return rtn
}

func (t *TPondStore) ToProto() *commonPB.PondStore {
	rtn := &commonPB.PondStore{
		PondId: t.PondId,
	}
	for _, batch := range t.PondBatches {
		rtn.List = append(rtn.List, batch.ToProto())
	}
	return rtn
}

type PondStoreBuyVo struct {
	Reward   *commonPB.Reward `json:"reward"`
	PondGood *PoodGood        `json:"pond_good"`
	Bought   int32            `json:"bought"`
}
