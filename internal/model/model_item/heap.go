package modelItem

import (
	"encoding/json"
	"time"
)

// TItemHeapDurable 这里是道具堆耐久百分比
type TItemHeapDurable struct {
	PlayerId   uint64 `xorm:"pk not null comment('玩家ID') BIGINT(20)" json:"player_id"`          // 玩家ID
	ItemId     int64   `xorm:"pk not null comment('道具ID') BIGINT(20)" json:"item_id"`           // 道具ID
	DurablePer int32   `xorm:"not null comment('耐久百分比') INT(10)" json:"durable_per"`          // 耐久百分比
	UpdateTime time.Time `xorm:"not null comment('更新时间') DATETIME updated" json:"update_time"` // 更新时间
}

// NewItemHeapDurableFromJsonStr 从json字符串解析出堆耐久百分比数据结构
func NewItemHeapDurableFromJsonStr(jsonStr string) *TItemHeapDurable {
	heapDurable := &TItemHeapDurable{}
	err := json.Unmarshal([]byte(jsonStr), heapDurable)
	if err != nil {
		return nil
	}

	return heapDurable
}

// ToJsonStr 转化为json字符串
func (heapDurable *TItemHeapDurable) ToJsonStr() string {
	if heapDurable == nil {
		return ""
	}

	jsonBytes, err := json.Marshal(heapDurable)
	if err != nil {
		return ""
	}

	return string(jsonBytes)
}