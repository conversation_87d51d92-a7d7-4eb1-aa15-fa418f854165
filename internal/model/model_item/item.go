package modelItem

import (
	"context"
	"encoding/json"
	"fmt"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	"github.com/sirupsen/logrus"
)

// 道具修改参数
type ItemOptParam struct {
	ItemId       int64           `json:"item_id"`       // 道具ID
	ItemNum      int64           `json:"item_num"`      // 道具数量
	CategoryType int32           `json:"category_type"` // 道具分类
	ItemType     int32           `json:"item_type"`     // 道具类型
	ItemSubType  int32           `json:"item_sub_type"` // 道具子类型
	ExpireTime   int64           `json:"expire_time"`   // 过期时间
	InstanceId   string          `json:"instance_id"`   // 实例id
	Extra        map[int32]int64 `json:"extra"`         // 额外属性
}

// NewItemOptParam 初始化道具修改参数
func NewItemOptParam(ctx context.Context, itemId int64, itemNum int64, isUnpack bool) ([]*ItemOptParam, error) {
	entry := logx.NewLogEntry(ctx)
	itemAllConf := cmodel.GetAllItem(consul_config.WithGrpcCtx(ctx))
	if itemAllConf == nil {
		entry.Errorf("get item config failed")
		return nil, protox.CodeError(commonPB.ErrCode_ERR_CONF_ERROR, "get item config failed")
	}

	// 查询道具配置
	itemConf := itemAllConf[int64(itemId)]
	if itemConf == nil {
		entry.Errorf("get item config failed, itemId: %d", itemId)
		return nil, protox.CodeError(commonPB.ErrCode_ERR_CONF_ERROR, fmt.Sprintf("itemCfg Id: %d", itemId))
	}

	//礼包且拆包
	if itemConf.ItemType == int32(commonPB.ITEM_TYPE_IT_REWARD_BAG) && isUnpack {
		// 查询礼包配置
		giftInfo := cmodel.GetGiftBasic(int64(itemId), consul_config.WithGrpcCtx(ctx))
		if giftInfo == nil {
			entry.Errorf("get gift config failed, itemId: %d", itemId)
			return nil, protox.CodeError(commonPB.ErrCode_ERR_CONF_ERROR, fmt.Sprintf("giftCfg Id: %d", itemId))
		}

		// 组合道具
		itemList := make([]*ItemOptParam, 0, len(giftInfo.Item))
		for _, item := range giftInfo.Item {
			itemId := item.ItemId
			// 道具配置
			itemConf := itemAllConf[int64(itemId)]
			if itemConf == nil {
				entry.Errorf("get item config failed, itemId: %d", itemId)
				return nil, protox.CodeError(commonPB.ErrCode_ERR_CONF_ERROR, fmt.Sprintf("itemCfg Id: %d", itemId))
			}
			itemList = append(itemList, &ItemOptParam{
				ItemId:       itemId,
				ItemNum:      item.Count * itemNum,
				CategoryType: int32(itemConf.CategoryId),
				ItemType:     int32(itemConf.ItemType),
				ItemSubType:  int32(itemConf.SubType),
				ExpireTime:   itemConf.ExpireTime,
			})
		}
		return itemList, nil

	} else {
		return []*ItemOptParam{
			{
				ItemId:       itemId,
				ItemNum:      itemNum,
				CategoryType: int32(itemConf.CategoryId),
				ItemType:     int32(itemConf.ItemType),
				ItemSubType:  int32(itemConf.SubType),
				ExpireTime:   itemConf.ExpireTime,
			},
		}, nil
	}
}

// 基于配置构建
func NewItemOptByConf(ctx context.Context, itemId int64, itemNum int64) (*ItemOptParam, error) {
	entry := logx.NewLogEntry(ctx)
	itemConf := cmodel.GetItem(itemId, consul_config.WithGrpcCtx(ctx))
	if itemConf == nil {
		entry.Errorf("get item config failed")
		return nil, fmt.Errorf("get item config failed:%+v", itemId)
	}
	return &ItemOptParam{
		ItemId:       itemId,
		ItemNum:      itemNum,
		CategoryType: int32(itemConf.CategoryId),
		ItemType:     int32(itemConf.ItemType),
		ItemSubType:  int32(itemConf.SubType),
		ExpireTime:   itemConf.ExpireTime,
	}, nil
}

func ItemBase2JsonStr(itemParams []*commonPB.ItemBase) string {
	if len(itemParams) <= 0 {
		return ""
	}

	// 转化为json
	bytes, err := json.Marshal(itemParams)
	if err != nil {
		logrus.Errorf("to json failed, err:%s", err)
		return ""
	}

	return string(bytes)
}

func (t *ItemOptParam) ToJsonStr() string {
	if t == nil {
		return ""
	}

	// 转化为json
	bytes, err := json.Marshal(t)
	if err != nil {
		logrus.Errorf("to json failed, err:%s", err)
		return ""
	}

	return string(bytes)
}

// ToItemProto 转化位道具协议
func (t *ItemOptParam) ToItemProto() *commonPB.Item {
	if t == nil {
		return nil
	}

	return &commonPB.Item{
		ItemId:         t.ItemId,
		ItemCategory:   commonPB.ITEM_CATEGORY(t.CategoryType),
		ItemType:       commonPB.ITEM_TYPE(t.ItemType),
		ItemExpireTime: t.ExpireTime,
		Extra:          t.Extra,
		InstanceId:     t.InstanceId,
	}
}

// ToOriginLootProto 转化为原始道具proto
func (t *ItemOptParam) ToOriginLootProto() (*commonPB.OriginLoot, error) {
	itemProto := t.ToItemProto()
	if itemProto == nil {
		logrus.Errorf("to item proto failed, itemId:%d", t.ItemId)
		return nil, fmt.Errorf("to item proto failed")
	}

	return &commonPB.OriginLoot{
		Item:  itemProto,
		Value: t.ItemNum,
	}, nil
}

// MakeUpItem 组合道具(相同道具统一处理)
func MakeUpItem(itemList []*ItemOptParam) []*ItemOptParam {
	if len(itemList) <= 0 {
		logrus.Errorf("make up item failed, itemList is empty")
		return nil
	}

	itemMap := make(map[int64]*ItemOptParam, len(itemList))
	newItemList := make([]*ItemOptParam, 0)
	for _, item := range itemList {
		if item == nil || item.ItemId <= 0 {
			logrus.Errorf("make up item failed, item is empty")
			return nil
		}

		if item.InstanceId != "" {
			newItemList = append(newItemList, item)
		} else {
			// 相同道具合并
			if _, ok := itemMap[item.ItemId]; !ok {
				itemMap[item.ItemId] = item
			} else {
				itemMap[item.ItemId].ItemNum += item.ItemNum
			}
		}

	}

	for _, item := range itemMap {
		newItemList = append(newItemList, item)
	}

	return newItemList
}

// 复制内容
func (item *ItemOptParam) Copy() *ItemOptParam {
	return &ItemOptParam{
		ItemId:       item.ItemId,
		ItemNum:      item.ItemNum,
		CategoryType: item.CategoryType,
		ItemType:     item.ItemType,
		ItemSubType:  item.ItemSubType,
		ExpireTime:   item.ExpireTime,
		Extra:        make(map[int32]int64),
	}
}
