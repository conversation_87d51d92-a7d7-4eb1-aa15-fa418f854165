package model_ann

import (
	"time"
)

const TableAnnPopup = "t_ann_popup"

// AnnPopup 数据库表结构体
type AnnPopup struct {
	Id         int32     `xorm:"bigint autoincr pk comment('唯一标识')"`
	Priority   int32     `xorm:"notnull comment('优先级（数值越小越优先）')"`
	ChannelId  int32     `xorm:"notnull comment('渠道id')"`
	PopStyle   int32     `xorm:"notnull comment('默认1 有关闭按钮')"`
	Content    string    `xorm:"text notnull comment('内容（JSON 格式）')"`
	Action     string    `xorm:"text notnull comment('动作（JSON 格式）')"`
	Conditions string    `xorm:"text notnull comment('条件（JSON 格式）')"`
	Enable     bool      `xorm:"default(true) comment('是否启用')"`
	StartTime  int64     `xorm:"notnull comment('生效开始时间（秒级 Unix 时间戳）')"`
	EndTime    int64     `xorm:"notnull comment('生效结束时间（秒级 Unix 时间戳）')"`
	CreatedAt  time.Time `xorm:"notnull"`
	UpdatedAt  time.Time `xorm:"notnull"`
}
