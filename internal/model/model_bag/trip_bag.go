package model_bag

import (
	"context"
	"encoding/json"
	"fmt"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
)

const TableTripBag = "t_trip_bag"

type TTripBag struct {
	PlayerId   uint64 `xorm:"pk comment('用户id')" json:"player_id"`
	BagType    int32  `xorm:"pk comment('背包类型')" json:"bag_type"`
	ItemId     int64  `xorm:"pk comment('道具id')" json:"item_id"`
	InstanceId string `xorm:"pk comment('实例id')" json:"instance_id"`
	Count      int64  `xorm:"comment('数量')" json:"count"`
	CreateAt   int64  `xorm:"comment('创建时间') created" json:"create_at"`
	UpdateAt   int64  `xorm:"comment('更新时间') updated" json:"update_at"`
}

func NewTripBag() *TTripBag {
	return &TTripBag{}
}

func (t *TTripBag) String() string {
	return fmt.Sprintf("{bagType:%d, itemId:%d, instanceId:%s, count:%d}", t.BagType, t.ItemId, t.InstanceId, t.Count)
}

// 数据库名称
func (t *TTripBag) TableName() string {
	return TableTripBag
}

func (t *TTripBag) DBBean() *TTripBag {
	return &TTripBag{
		PlayerId:   t.PlayerId,
		BagType:    t.BagType,
		ItemId:     t.ItemId,
		InstanceId: t.InstanceId,
	}
}

func (t *TTripBag) FromItemBase(item *commonPB.ItemBase) {
	// TODO 自动赋值bagtype
	t.ItemId = item.ItemId
	t.InstanceId = item.InstanceId
	t.Count = item.ItemCount
}

func (t *TTripBag) FromItem(item *commonPB.Item) {
	t.ItemId = item.ItemId
	t.InstanceId = item.InstanceId
}

func (t *TTripBag) ToHash() []byte {
	js, _ := json.Marshal(t)
	return js
}

func (t *TTripBag) FormHash(s string) error {
	err := json.Unmarshal([]byte(s), t)
	if err != nil {
		return err
	}
	return nil
}

func (t *TTripBag) ToItemInfo(ctx context.Context) *commonPB.ItemInfo {
	// XXX: 道具通常不会根据不同渠道不一样
	// cfg := cmodel.GetItem(t.ItemId, consul_config.WithGrpcCtx(ctx))

	return &commonPB.ItemInfo{
		Item: &commonPB.Item{
			ItemId:     t.ItemId,
			InstanceId: t.InstanceId,
			// TODO
			// ItemCategory: ,
			// ItemType: ,
		},
		ItemCount: t.Count,
	}
}

func ToItemInfoList(list []*TTripBag) []*commonPB.ItemInfo{
	l := make([]*commonPB.ItemInfo, len(list))
	for i, bagItem:=range list{
		l[i] = bagItem.ToItemInfo(nil)
	}
	return l
}