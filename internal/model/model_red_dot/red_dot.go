package model_red_dot

import (
	"fmt"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	hallPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/hall"
)

// SubModuleType 表示子模块类型，用于标识模块下的具体功能点
type SubModuleType int32

// RedDotInfo 红点信息结构体
type RedDotInfo struct {
	PlayerID      uint64                    // 玩家ID
	ModuleType    commonPB.USER_MODULE_TYPE // 主模块类型
	SubModuleType SubModuleType             // 子模块类型
	HasRedDot     bool                      // 是否有红点
}

// ModuleConfig 模块配置信息
type ModuleConfig struct {
	Enabled bool // 是否启用该模块
}

// GetBitmapPosition 计算在bitmap中的位置
// 使用主模块*1000 + 子模块的方式拼接
func GetBitmapPosition(moduleType commonPB.USER_MODULE_TYPE, subModuleType SubModuleType) uint64 {
	// 检查模块ID和子模块ID是否在范围内
	if uint64(moduleType) > 999 {
		moduleType = commonPB.USER_MODULE_TYPE(999)
	}
	if uint64(subModuleType) > 999 {
		subModuleType = SubModuleType(999)
	}
	return uint64(moduleType)*1000 + uint64(subModuleType)
}

// GetModuleAndSubModule 从位置中提取模块类型和子模块类型
func GetModuleAndSubModule(position int) (commonPB.USER_MODULE_TYPE, SubModuleType) {
	moduleType := commonPB.USER_MODULE_TYPE(position / 1000)
	subModuleType := SubModuleType(position % 1000)
	return moduleType, subModuleType
}

// FormatBitmapPosition 将位置格式化为易读的字符串，用于调试
func FormatBitmapPosition(moduleType commonPB.USER_MODULE_TYPE, subModuleType SubModuleType) string {
	position := GetBitmapPosition(moduleType, subModuleType)
	return fmt.Sprintf("moduleType=%d, subModuleType=%d, position=%d",
		moduleType, subModuleType, position)
}

// BuildRedDotInfos 构建红点信息
func BuildRedDotInfos(positions []uint64) []*hallPB.RedDotInfo {
	redDots := make([]*hallPB.RedDotInfo, 0, len(positions))
	for _, pos := range positions {
		md, smd := GetModuleAndSubModule(int(pos))
		redDots = append(redDots, &hallPB.RedDotInfo{
			ModuleType:    md,
			SubModuleType: int32(smd),
			HasRedDot:     true,
		})
	}
	return redDots
}

// CalcPosition 计算红点在bitmap中的位置
func CalcPosition(moduleType commonPB.USER_MODULE_TYPE, subModuleType SubModuleType) uint64 {
	return GetBitmapPosition(moduleType, subModuleType)
}
