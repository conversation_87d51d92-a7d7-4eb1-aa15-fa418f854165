package rpc

import (
	"context"
	"hallsrv/internal/services"

	hallRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/hallrpc"
	"git.keepfancy.xyz/back-end/frameworks/kit/rpc"
)

type HallRpcServer struct {
}

func InitHallRpc() {
	hallRpcService := &HallRpcServer{}
	hallRpc.RegisterHallServiceServer(rpc.Server, hallRpcService)
}

func (h *HallRpcServer) OptPlayerItem(ctx context.Context, req *hallRpc.OptPlayerItemReq) (*hallRpc.OptPlayerItemRsp, error) {
	return services.GetHallServiceInstance().OptPlayerItem(ctx, req)
}

func (h *HallRpcServer) QueryPlayerBaseInfo(ctx context.Context, req *hallRpc.QueryPlayerBaseInfoReq) (*hallRpc.QueryPlayerBaseInfoRsp, error) {
	return services.GetHallServiceInstance().QueryPlayerBaseInfo(ctx, req)
}

func (h *HallRpcServer) GmLoadRule(ctx context.Context, req *hallRpc.GmLoadRuleReq) (*hallRpc.GmLoadRuleRsp, error) {
	return services.GetHallServiceInstance().GmLoadRigRule(ctx, req)
}

// QueryPlayerRodInfo 查询用户背包钓组信息
func (h *HallRpcServer) QueryPlayerRodInfo(ctx context.Context, req *hallRpc.QueryPlayerRodInfoReq) (*hallRpc.QueryPlayerRodInfoRsp, error) {
	return services.GetHallServiceInstance().QueryPlayerRodInfo(ctx, req)
}

// QueryHolidayType 查询节假日类型
func (h *HallRpcServer) QueryHolidayType(ctx context.Context, req *hallRpc.QueryHolidayTypeReq) (*hallRpc.QueryHolidayTypeRsp, error) {
	return services.GetHallServiceInstance().QueryHolidayType(ctx, req)
}

// 磨损钓竿耐久
func (h *HallRpcServer) LossRodDurability(ctx context.Context, req *hallRpc.LossRodDurabilityReq) (rsp *hallRpc.LossRodDurabilityRsp, err error) {
	return services.GetHallServiceInstance().LossRodDurability(ctx, req)
}

// 扣除鱼饵耐久
func (h *HallRpcServer) LossItemHeap(ctx context.Context, req *hallRpc.LossItemHeapReq) (rsp *hallRpc.LossItemHeapRsp, err error) {
	return services.GetHallServiceInstance().LossItemHeap(ctx, req)
}

// 设置玩家红点状态
func (h *HallRpcServer) SetPlayerRedDot(ctx context.Context, req *hallRpc.SetPlayerRedDotReq) (rsp *hallRpc.SetPlayerRedDotRes, err error) {
	return services.GetHallServiceInstance().SetPlayerRedDot(ctx, req)
}
