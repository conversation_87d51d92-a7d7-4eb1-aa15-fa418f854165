package dao_identity

import (
	"errors"
	model "hallsrv/internal/model/model_identity"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_mysql"
	"git.keepfancy.xyz/back-end/frameworks/dict"
	"git.keepfancy.xyz/back-end/frameworks/kit/mysql"
	"github.com/ldy105cn/xorm"
	"github.com/sirupsen/logrus"
)

func getMysqlRealNameSession() (*xorm.Engine, error) {
	return mysql.GetDefaultMgr().GetMyEngine(dict_mysql.MysqlDBGeneral)
}

// SyncRealNameAuthTable 同步数据库表单
func SyncRealNameAuthTable() {
	engine, err := getMysqlRealNameSession()
	if engine == nil || err != nil {
		logrus.Errorf("mysql table engine is empty")
		return
	}

	fcmTable := &model.TPlayerRealNameAuth{}
	if err := engine.Sync2(fcmTable); err != nil {
		logrus.Errorf("engine.Sync2(fcmTable) error(%v)", err)
	} else {
		logrus.Infof("sync fcmTable success")
	}
}
func Insert(info *model.TPlayerRealNameAuth) error {
	entry := logrus.WithFields(logrus.Fields{
		dict.SysWordPlayerID: info.PlayerId,
	})
	entry.Infof("Insert fcm info : %d", info.PlayerId)

	session, _ := getMysqlRealNameSession()
	_, err := session.Table(model.TableTPlayFcm).Insert(info)
	if err != nil {
		entry.WithError(err).Errorf("inset fcm error ")
		return errors.New("inset fcm error")
	}
	return nil
}

func Load(playerId uint64, productId int32) (bool, error) {
	entry := logrus.WithFields(logrus.Fields{
		dict.SysWordPlayerID: playerId,
	})
	entry.Infof("Load fcm info : %d", playerId)

	session, _ := getMysqlRealNameSession()
	exist, err := session.Table(model.TableTPlayFcm).Get(&model.TPlayerRealNameAuth{PlayerId: playerId, ProductId: productId})
	if err != nil {
		entry.WithError(err).Errorf("inset fcm error ")
		return false, errors.New("inset fcm error")
	}
	return exist, nil
}

func UpdateDBPlayerFcmDao(info *model.TPlayerRealNameAuth, fields ...string) (int, error) {
	entry := logrus.WithFields(logrus.Fields{
		dict.SysWordPlayerID: info.PlayerId,
	})
	entry.Infof("Update fcm info : %d", info.PlayerId)

	session, _ := getMysqlRealNameSession()

	aff, err := session.Table(model.TableTPlayFcm).
		Where("player_id = ? ", info.PlayerId).
		Cols(fields...).
		Update(info)
	if err != nil {
		entry.WithError(err).Errorf("update error")
		return 0, err
	}

	if aff == 0 {
		entry.Infof("no date update")
		return 0, nil
	}

	return int(aff), nil
}
