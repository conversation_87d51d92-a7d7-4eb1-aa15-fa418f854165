package daoPlayer

import (
	"context"
	"hallsrv/internal/config"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
)

// CreatePlayerStateBmStrRds 创建玩家指定字段的bitmap信息
func CreatePlayerStateBmStrRds(ctx context.Context, playerId uint64, bm map[string]interface{}) {
	entry := logx.NewLogEntry(ctx)
	pipLine := redisx.GetPlayerCli().TxPipeline()

	pbKey := config.GetPlayerBitmapRdsKey(playerId)
	pipLine.HSet(ctx, config.GetPlayerBitmapRdsKey(playerId), bm)
	pipLine.Expire(ctx, pbKey, config.PLAYER_STATE_BITMAP_EXPIRE)

	if _, err := pipLine.Exec(ctx); err != nil {
		entry.Errorf("create player:%d state bm info bm:%s, err:%+v", playerId, bm, err)
	}
}

// DeletePlayerStateBmStrRds 删除玩家bitmap信息
func DeletePlayerStateBmStrRds(ctx context.Context, playerId uint64) error {
	entry := logx.NewLogEntry(ctx)
	err := redisx.GetPlayerCli().Del(ctx, config.GetPlayerBitmapRdsKey(playerId)).Err()
	if err != nil {
		entry.Errorf("delete player:%d state bm err: %+v", playerId, err)
	}
	return err
}

func QueryPlayerStateBmStrRds(ctx context.Context, playerId uint64) (map[string]string, error) {
	// 查询玩家的bitmap信息
	return redisx.GetPlayerCli().HGetAll(ctx, config.GetPlayerBitmapRdsKey(playerId)).Result()
}
