package daoPlayer

import (
	"context"
	"hallsrv/internal/config"
	modelPlayer "hallsrv/internal/model/model_player"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_mysql"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/frameworks/kit/mysql"
	"git.keepfancy.xyz/back-end/frameworks/kit/singleflight"
	"github.com/ldy105cn/xorm"
)

// QueryPlayerStateBmStrRdb 查询玩家状态
func QueryPlayerStateBmStrRdb(ctx context.Context, playerId uint64) (*modelPlayer.PlayerStateBm, error) {

	var playerStateBm modelPlayer.PlayerStateBm
	engine := mysql.GetMysqlEngine(dict_mysql.MysqlDBGeneral)
	_, err := engine.Table(modelPlayer.TablePlayerState).
		Where("player_id = ?", playerId).
		Get(&playerStateBm)

	return &playerStateBm, err
}

func InsertOrUpdatePlayerStateBmStrRdb(ctx context.Context, playerId uint64, rdbField, bmStr string) error {
	entry := logx.NewLogEntry(ctx)

	key := config.CreatePlayerStateBmSingleflight(playerId)

	_, err, _ := singleflight.Do(key, func() (interface{}, error) {
		engine := mysql.GetMysqlEngine(dict_mysql.MysqlDBGeneral)
		_, err := engine.Transaction(func(session *xorm.Session) (interface{}, error) {
			// 查询玩家基础信息
			has, err := session.Exist(&modelPlayer.PlayerStateBm{PlayerId: playerId})
			if err != nil {
				entry.Errorf("failed to check player state bm exist for playerId: %d, err:%+v", playerId, err)
				return nil, err
			}
			if has {
				// 存在更新
				_, err = session.Table(modelPlayer.TablePlayerState).
					Where("player_id = ?", playerId).
					Update(map[string]interface{}{rdbField: bmStr})
			} else {
				// 不存在插入
				_, err = session.Table(modelPlayer.TablePlayerState).
					Insert(map[string]interface{}{"player_id": playerId, rdbField: bmStr})
			}
			return nil, err
		})
		return nil, err
	})

	if err != nil {
		entry.Errorf("failed to insert or update player state bm for playerId: %d, err:%+v", playerId, err)
		return err
	}

	entry.Infof("insert or update player state bm success for playerId: %d, rdbField: %s, bmStr: %s", playerId, rdbField, bmStr)
	return nil
}
