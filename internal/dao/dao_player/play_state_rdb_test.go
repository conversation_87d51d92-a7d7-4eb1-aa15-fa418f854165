package daoPlayer

import (
	"context"
	modelPlayer "hallsrv/internal/model/model_player"
	test_init "hallsrv/internal/test"
	"testing"
)

func TestInsertOrUpdatePlayerStateBm(t *testing.T) {
	test_init.InitRedisConsul()
	test_init.InitSql()

	ctx := context.Background()
	var playerId uint64 = 999
	// 玩家经验和等级
	rdbField := modelPlayer.GetPlayerStateBmExpLvRdsField()
	got, err := QueryPlayerStateBmStrRdb(ctx, playerId)
	t.Logf("got:%+v, err:%v", got, err)

	err = InsertOrUpdatePlayerStateBmStrRdb(ctx, playerId, rdbField, "ev")
	t.Logf("err:%v", err)

	// 玩家物品
	rdbField = modelPlayer.GetPlayerStateBmGoodsRdsField()
	got, err = QueryPlayerStateBmStrRdb(ctx, playerId)
	t.Logf("got:%v, err:%v", got, err)

	err = InsertOrUpdatePlayerStateBmStrRdb(ctx, playerId, rdbField, "goods")
	t.Logf("err:%v", err)

}
