package daoPond

import (
	"context"
	"errors"
	"fmt"
	"hallsrv/internal/config"
	modelPond "hallsrv/internal/model/model_pond"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
	"github.com/go-redis/redis/v8"
)

func fmtLastGameInfoRdsKey(playerId uint64) string {
	return fmt.Sprintf(config.RDS_KEY_LAST_GAME_INFO, playerId)
}

// QueryPlayerLastGameInfo 查询玩家上次游戏信息
func QueryPlayerLastGameInfo(ctx context.Context, playerId uint64) (*modelPond.LastGameInfo, error) {

	entry := logx.NewLogEntry(ctx)
	lastGameHash, err := redisx.GetGameCli().HGetAll(ctx, fmtLastGameInfoRdsKey(playerId)).Result()
	if errors.Is(err, redis.Nil) {
		entry.Debugf("query player:%d last game info not found", playerId)
		return &modelPond.LastGameInfo{}, nil
	}

	if err != nil {
		entry.Errorf("query player:%d last game info error: %v", playerId, err)
		return nil, err
	}

	// 根据redis hash 初始化
	lastGameInfo := modelPond.NewLastGameInfoFromRdsHash(lastGameHash)

	if lastGameInfo == nil {
		entry.Errorf("last game info is nil, data:%v", lastGameHash)
		return nil, errors.New("last game info is nil")
	}

	return lastGameInfo, nil
}

// UpdatePlayerLastGameInfo 修改玩家上次游戏信息
func UpdatePlayerLastGameInfo(ctx context.Context, playerId uint64, lastGameInfo *modelPond.LastGameInfo) error {
	entry := logx.NewLogEntry(ctx)
	if playerId == 0 || lastGameInfo == nil {
		entry.Errorf("update player:%d last game info error: playerId or lastGameInfo is nil", playerId)
		return errors.New("last game info is nil")
	}

	// 使用TxPipeline 设置后加上过期时间
	pipLine := redisx.GetGameCli().TxPipeline()

	// 将map转换为Redis命令所需的参数列表
	values := make([]interface{}, 0, len(lastGameInfo.ToRedisHashField())*2)
	for k, v := range lastGameInfo.ToRedisHashField() {
		values = append(values, k, v)
	}

	// 使用TxPipeline 设置后加上过期时间
	pipLine.HMSet(ctx, fmtLastGameInfoRdsKey(playerId), values)
	pipLine.Expire(ctx, fmtLastGameInfoRdsKey(playerId), config.LAST_GAME_INFO_EXPIRE)

	_, err := pipLine.Exec(ctx)
	if err != nil {
		entry.Errorf("update player:%d last game info:%v, error: %v", playerId, *lastGameInfo, err)
		return err
	}

	entry.Debugf("update player:%d last game info success: %v", playerId, *lastGameInfo)

	return err
}
