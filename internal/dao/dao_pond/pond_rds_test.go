package daoPond

import (
	"context"
	modelPond "hallsrv/internal/model/model_pond"
	test_init "hallsrv/internal/test"
	"testing"
)

func TestUpdatePlayerLastGameInfo(t *testing.T) {
	test_init.InitRedisConsul()
	ctx := context.TODO()
	playerId := uint64(179)
	lastGameInfo := modelPond.NewLastGameInfo(playerId, 1, 1, 1)
	err := UpdatePlayerLastGameInfo(ctx, playerId, lastGameInfo)

	t.Logf("update data:%v, ret:%v", *lastGameInfo, err)
}

func TestQueryPlayerLastGameInfo(t *testing.T) {
	ctx := context.TODO()
	playerId := uint64(179)
	lastGameInfo, err := QueryPlayerLastGameInfo(ctx, playerId)

	t.Logf("last game info:%v, err:%v", lastGameInfo, err)
}
