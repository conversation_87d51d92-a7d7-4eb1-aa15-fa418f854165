package dao_continuous_login

import (
	"context"
	"hallsrv/internal/model/model_continuous_login"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_mysql"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/frameworks/kit/mysql"
	"github.com/ldy105cn/xorm"
	"github.com/sirupsen/logrus"
)

func GetMysqlEngine() (*xorm.Engine, error) {
	return mysql.GetDefaultMgr().GetMyEngine(dict_mysql.MysqlDBGeneral)
}

func QueryDb(ctx context.Context, playerId uint64) (*model_continuous_login.TConstinuousLogin, error) {
	engine, err := GetMysqlEngine()
	if err != nil {
		logrus.Errorf("sql engine fail:%+v", err)
		return nil, protox.CodeError(commonPB.ErrCode_ERR_DB_OPE_ERROR, err.Error())
	}
	bean := &model_continuous_login.TConstinuousLogin{PlayerId: playerId}
	_, err = engine.Get(bean)
	if err != nil {
		return nil, protox.CodeError(commonPB.ErrCode_ERR_DB_OPE_ERROR, err.Error())
	}
	// 该系统可默认返回数据
	return bean, nil
}

func getBean(playerId uint64) *model_continuous_login.TConstinuousLogin {
	return &model_continuous_login.TConstinuousLogin{PlayerId: playerId}
}

func UpdateDb(ctx context.Context, updateBean *model_continuous_login.TConstinuousLogin) error {
	engine, err := GetMysqlEngine()
	if err != nil {
		logrus.Errorf("sql engine fail:%+v", err)
		return protox.CodeError(commonPB.ErrCode_ERR_DB_OPE_ERROR, err.Error())
	}

	_, err = engine.Transaction(func(session *xorm.Session) (interface{}, error) {
		bean := getBean(updateBean.PlayerId)
		has, err := session.Exist(bean)
		if err != nil {
			return nil, err
		}
		if has {
			_, err = session.Where("player_id = ?", bean.PlayerId).AllCols().Update(updateBean)
		} else {
			_, err = session.Insert(updateBean)
		}
		return nil, err
	})
	if err != nil {
		logrus.Errorf("failed to update db for err:%+v", err)
		return protox.CodeError(commonPB.ErrCode_ERR_DB_OPE_ERROR, err.Error())
	}

	return nil
}
