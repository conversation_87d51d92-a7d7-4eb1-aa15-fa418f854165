package dao_continuous_login

import (
	"context"
	"encoding/json"
	"fmt"
	"hallsrv/internal/config"
	"hallsrv/internal/model/model_continuous_login"
	"time"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
)

func GetRedisCli() *redisx.Client {
	return redisx.GetGeneralCli()
}

func redisKey(playerId uint64) string {
	return fmt.Sprintf(config.RDS_KEY_CONTINUOUS_LOGIN, playerId)
}

func QueryRds(ctx context.Context, playerId uint64) (*model_continuous_login.TConstinuousLogin, error) {
	cli := GetRedisCli()
	data, err := cli.Get(ctx, redisKey(playerId)).Result()
	if err != nil {
		return nil, err
	}

	bean := &model_continuous_login.TConstinuousLogin{}
	err = json.Unmarshal([]byte(data), bean)
	if err != nil {
		return nil, err
	}
	return bean, nil
}

func UpdateRds(ctx context.Context, bean *model_continuous_login.TConstinuousLogin) error {
	if bean == nil {
		return fmt.Errorf("update continuous login rds bean is nil")
	}

	data, err := json.Marshal(bean)
	if err != nil {
		return err
	}

	cli := GetRedisCli()
	return cli.Set(ctx, redisKey(bean.PlayerId), string(data), time.Hour*24*7).Err()
}

func DeleteRds(ctx context.Context, playerId uint64) error {
	cli := GetRedisCli()
	return cli.Del(ctx, redisKey(playerId)).Err()
}
