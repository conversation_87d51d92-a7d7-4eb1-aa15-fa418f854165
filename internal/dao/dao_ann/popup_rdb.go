package dao_ann

import (
	"fmt"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_mysql"
	"git.keepfancy.xyz/back-end/frameworks/kit/mysql"
	"github.com/ldy105cn/xorm"
	"hallsrv/internal/config"
	modelAnn "hallsrv/internal/model/model_ann"
)

func getMysqlPopUpSession() (*xorm.Engine, error) {
	return mysql.GetDefaultMgr().GetMyEngine(dict_mysql.MysqlDBGeneral)
}

// QueryAnnPopUpInfoRdb 查询拍脸图
func QueryAnnPopUpInfoRdb(channelId int32) ([]*modelAnn.AnnPopup, error) {

	engine, err := getMysqlPopUpSession()
	if engine == nil || err != nil {
		return nil, fmt.Errorf("mysql table engine is empty")
	}

	session := engine.Table(config.TableAnnPopup)

	var PopUpInfos []*modelAnn.AnnPopup
	// 开启的拍脸图
	err = session.Where("channel_id=? and enable=? ", channelId, true).Find(&PopUpInfos)
	if err != nil {
		return nil, err
	}

	return PopUpInfos, nil
}
