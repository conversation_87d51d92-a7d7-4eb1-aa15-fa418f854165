package dao_pond_store

import (
	"context"
	"encoding/json"
	"fmt"
	"hallsrv/internal/config"
	"hallsrv/internal/model/model_pond_store"
	"time"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
	"git.keepfancy.xyz/back-end/frameworks/lib/random"
	"git.keepfancy.xyz/back-end/frameworks/lib/transform"
	"github.com/go-redis/redis/v8"
	"github.com/sirupsen/logrus"
)

func getRdsCli(ctx context.Context) *redisx.Client {
	cli := redisx.GetGeneralCli()
	if cli == nil {
		logrus.Errorf("redis client is nil")
		return nil
	}
	return cli
}

// QueryPondMultiStore 查询多个钓场商城数据
func QueryPondMultiStoreGood(ctx context.Context, channel int32, pondIds []int64) (map[int64]*model_pond_store.TPondStore, error) {
	entry := logx.NewLogEntry(ctx)
	cli := getRdsCli(ctx)
	if cli == nil {
		entry.Errorf("redis client is nil")

		return nil, protox.CodeError(commonPB.ErrCode_ERR_DB_OPE_ERROR, "redis client is nil")
	}
	pipe := cli.Pipeline()
	for _, pondId := range pondIds {
		pipe.HGetAll(ctx, config.GetRdsKeyPondStoreGoods(channel, pondId))
	}

	cmds, err := pipe.Exec(ctx)
	if err != nil {
		return nil, err
	}
	// 数据转换
	pondStoreMap := make(map[int64]*model_pond_store.TPondStore, len(pondIds))
	for i, cmd := range cmds {
		result := cmd.(*redis.StringStringMapCmd)
		val, err := result.Result()
		if err != nil {
			// 查询失败
			entry.Errorf("query pond store error %s", err.Error())
			return nil, protox.CodeError(commonPB.ErrCode_ERR_DB_OPE_ERROR, fmt.Sprintf("query pond store error %s", err.Error()))
		}
		if len(val) == 0 {
			continue
		}
		pondStore, err := deserializePondStoreGoods(val)
		if err != nil {
			entry.Errorf("deserialize pond store error %s", err.Error())
			return nil, err
		}
		pondStore.PondId = pondIds[i]
		pondStoreMap[pondStore.PondId] = pondStore
	}

	return pondStoreMap, nil
}

// UpdatePondStore 更新钓场商城数据
func UpdatePondStoreGoods(ctx context.Context, channel int32, pondId int64, pondStore *model_pond_store.TPondStore) error {
	cli := getRdsCli(ctx)
	if cli == nil {
		return protox.CodeError(commonPB.ErrCode_ERR_DB_OPE_ERROR, "redis client is nil")
	}

	pondStoreMap, err := serializePondStoreGoods(pondStore)
	if err != nil {
		return err
	}
	var lastTs int64 = 0
	for _, batch := range pondStore.PondBatches {
		if batch.TsEnd > lastTs {
			lastTs = batch.TsEnd
		}
	}
	randTs := random.Int64n(300)
	lastTs = lastTs + 3600*2 + randTs

	key := config.GetRdsKeyPondStoreGoods(channel, pondId)
	if len(pondStoreMap) > 0 {
		pipe := cli.Pipeline()
		pipe.HSet(ctx, key, pondStoreMap)
		pipe.ExpireAt(ctx, key, time.Unix(lastTs, 0))
		_, err = pipe.Exec(ctx)
		if err != nil {
			return err
		}
	} else {
		err := cli.Del(ctx, key).Err()
		if err != nil {
			return err
		}
	}

	return nil
}

// 序列化数据
func serializePondStoreGoods(pondStore *model_pond_store.TPondStore) ([]any, error) {
	pondStoreMap := make([]any, 0, len(pondStore.PondBatches)*2)
	for k, v := range pondStore.PondBatches {
		json, err := json.Marshal(v)
		if err != nil {
			return nil, protox.CodeError(commonPB.ErrCode_ERR_JSON_PARSE_ERROR, fmt.Sprintf("serialize pond store error, %s", err.Error()))
		}
		pondStoreMap = append(pondStoreMap, k, string(json))
	}

	return pondStoreMap, nil
}

// 反序列化数据
func deserializePondStoreGoods(result map[string]string) (*model_pond_store.TPondStore, error) {
	pondStore := model_pond_store.NewPondStore(0)
	for k, v := range result {
		key := transform.Str2Int64(k)
		batch := &model_pond_store.PondBatch{}
		err := json.Unmarshal([]byte(v), batch)
		if err != nil {
			return nil, protox.CodeError(commonPB.ErrCode_ERR_JSON_PARSE_ERROR, fmt.Sprintf("deserialize pond store error, %s", err.Error()))
		}
		pondStore.PondBatches[key] = batch
	}
	return pondStore, nil
}
