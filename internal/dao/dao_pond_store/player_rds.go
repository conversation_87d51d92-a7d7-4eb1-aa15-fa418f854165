package dao_pond_store

import (
	"context"
	"encoding/json"
	"fmt"
	"hallsrv/internal/config"
	"hallsrv/internal/model/model_pond_store"
	"time"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/frameworks/lib/random"
	"git.keepfancy.xyz/back-end/frameworks/lib/transform"
	"github.com/go-redis/redis/v8"
)

// 查询玩家购买数据
func QueryPlayerPondStore(ctx context.Context, playerId uint64, pondIds []int64) (map[int64]*model_pond_store.PondStoreBuy, error) {
	entry := logx.NewLogEntry(ctx)
	cli := getRdsCli(ctx)
	if cli == nil {
		return nil, fmt.Errorf("redis client is nil")
	}
	pipe := cli.Pipeline()
	for _, pondId := range pondIds {
		pipe.HGetAll(ctx, config.GetRdsKeyPlayerPondStore(playerId, pondId))
	}
	cmds, err := pipe.Exec(ctx)
	if err != nil {
		return nil, err
	}
	now := time.Now().Unix()
	rtn := make(map[int64]*model_pond_store.PondStoreBuy, len(pondIds))
	for i, cmd := range cmds {
		result := cmd.(*redis.StringStringMapCmd)
		val, err := result.Result()
		if err != nil {
			entry.Errorf("query player pond store error %s", err.Error())
			return nil, protox.CodeError(commonPB.ErrCode_ERR_DB_OPE_ERROR, fmt.Sprintf("query player pond store error %s", err.Error()))
		}
		pondStore, err := deserializePlayerPondStore(val)
		if err != nil {
			entry.Errorf("deserialize player pond store error %s", err.Error())
			return nil, err
		}
		// 过滤已失效内容
		for batchId, playerPondBought := range pondStore.Bought {
			if playerPondBought.TsEnd < now || playerPondBought.TsStart > now {
				delete(pondStore.Bought, batchId)
			}
		}
		pondStore.PondId = pondIds[i]
		rtn[pondStore.PondId] = pondStore
	}
	return rtn, nil
}

// UpdatePlayerPondStore 更新玩家购买数据
func UpdatePlayerPondStore(ctx context.Context, playerId uint64, pondId int64, pondStore *model_pond_store.PondStoreBuy) error {
	if len(pondStore.Bought) == 0 {
		return nil
	}
	cli := getRdsCli(ctx)
	if cli == nil {
		return protox.CodeError(commonPB.ErrCode_ERR_DB_OPE_ERROR, "redis client is nil")
	}
	serializePondStore, err := serializePlayerPondStore(pondStore)
	if err != nil {
		return err
	}
	var lastExpire int64 = 0
	for _, playerPondBought := range pondStore.Bought {
		if playerPondBought.TsEnd > lastExpire {
			lastExpire = playerPondBought.TsEnd
		}
	}

	key := config.GetRdsKeyPlayerPondStore(playerId, pondId)
	pipe := cli.Pipeline()
	pipe.HSet(ctx, key, serializePondStore)
	randTs := random.Int64n(300)
	pipe.ExpireAt(ctx, key, time.Unix(lastExpire, 0).Add(time.Duration(randTs)*time.Second+time.Hour*12))
	_, err = pipe.Exec(ctx)
	if err != nil {
		return err
	}
	return nil
}

// 序列化数据
func serializePlayerPondStore(pondStore *model_pond_store.PondStoreBuy) ([]any, error) {
	list := make([]any, 0, len(pondStore.Bought))
	for batchId, playerPondBought := range pondStore.Bought {
		jsonStr, err := json.Marshal(playerPondBought)
		if err != nil {
			return nil, protox.CodeError(commonPB.ErrCode_ERR_JSON_PARSE_ERROR, fmt.Sprintf("serialize player pond store error, %s", err.Error()))
		}
		list = append(list, batchId, string(jsonStr))
	}

	return list, nil
}

// 反序列化数据
func deserializePlayerPondStore(result map[string]string) (*model_pond_store.PondStoreBuy, error) {
	rtn := model_pond_store.NewPondStoreBuy(0)
	for k, v := range result {
		batchId := transform.Str2Int64(k)
		playerPondBought := model_pond_store.PlayerPondBought{}
		err := json.Unmarshal([]byte(v), &playerPondBought)
		if err != nil {
			return nil, protox.CodeError(commonPB.ErrCode_ERR_JSON_PARSE_ERROR, fmt.Sprintf("deserialize player pond store error, %s", err.Error()))
		}
		rtn.Bought[batchId] = playerPondBought
	}
	return rtn, nil
}
