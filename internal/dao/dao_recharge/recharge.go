package daoRecharge

import (
	"context"
	"errors"
	"fmt"
	"hallsrv/internal/config"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
	"git.keepfancy.xyz/back-end/frameworks/lib/timex"
	"github.com/go-redis/redis/v8"
)

func fmtMonthRechargeKey(playerId uint64) string {
	return fmt.Sprintf(config.RDS_KEY_MONTH_RECHARGE, timex.Now().Format("200601"), playerId)
}

// QueryPlayerMonthRecharge 查询玩家月充值总金额(分)
func QueryPlayerMonthRechargeRds(ctx context.Context, playerId uint64) (int64, error) {
	redisCli := redisx.GetGeneralCli()
	totalAmt, err := redisCli.Get(ctx, fmtMonthRechargeKey(playerId)).Int64()
	if errors.Is(err, redis.Nil) {
		return 0, nil
	}

	return totalAmt, err
}

// AddPlayerMonthRechargeRds 增加玩家月充值总金额(分)
func AddPlayerMonthRechargeRds(ctx context.Context, playerId uint64, amount int64) error {
	pipLine := redisx.GetGeneralCli().TxPipeline()
	pipLine.IncrBy(ctx, fmtMonthRechargeKey(playerId), amount)
	pipLine.Expire(ctx, fmtMonthRechargeKey(playerId), config.MONTH_RECHARGE_EXPIRE)

	_, err := pipLine.Exec(ctx)
	
	return err
}
