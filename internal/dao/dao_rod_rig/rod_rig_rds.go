package daoRodRig

import (
	"context"
	"fmt"
	"hallsrv/internal/config"
	modelRodRig "hallsrv/internal/model/model_rod_rig"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
	"git.keepfancy.xyz/back-end/frameworks/lib/transform"
)

func FmtRodRigInfoRdsKey(playerId uint64) string {
	return fmt.Sprintf(config.RDS_KEY_ROD_RIG_INFO, playerId)
}

// QueryPlayerRodRigRds 查询玩家竿组信息
func QueryPlayerRodRigRds(ctx context.Context, playerId uint64) ([]*modelRodRig.TRodRigInfo, error) {
	entry := logx.NewLogEntry(ctx)
	rodRigHash, err := redisx.GetGeneralCli().HGetAllWithNil(ctx, FmtRodRigInfoRdsKey(playerId)).Result()

	if err != nil {
		entry.Warnf("query player rod rig rds failed: %v", err)
		return nil, err
	}

	rodRigList := make([]*modelRodRig.TRodRigInfo, 0)
	for _, rodRigInfo := range rodRigHash {
		rodRigMod := modelRodRig.NewRodRigInfoFromJson(rodRigInfo)
		if rodRigMod == nil {
			entry.Errorf("pack player:%d rod rig info:%s error", playerId, rodRigInfo)
			continue
		}
		rodRigList = append(rodRigList, modelRodRig.NewRodRigInfoFromJson(rodRigInfo))
	}

	return rodRigList, nil
}

// UpdatePlayerRodRigRds 更新玩家钓组信息
func UpdatePlayerRodRigRds(ctx context.Context, playerId uint64, rodRigInfo *modelRodRig.TRodRigInfo) error {
	entry := logx.NewLogEntry(ctx)
	if rodRigInfo == nil {
		return fmt.Errorf("update player:%d rod rig info is nil", playerId)
	}

	pipLine := redisx.GetGeneralCli().TxPipeline()
	pipLine.HSet(ctx, FmtRodRigInfoRdsKey(playerId), rodRigInfo.ToJsonStr())
	pipLine.Expire(ctx, FmtRodRigInfoRdsKey(playerId), config.ROD_RIG_INFO_EXPIRE)

	_, err := pipLine.Exec(ctx)
	if err != nil {
		entry.Errorf("update player:%d rod rig info:%+v error: %v", playerId, rodRigInfo, err)
	}

	return err
}

// CachePlayerRodRigInfoRds 缓存玩家钓组信息
func CachePlayerRodRigInfoRds(ctx context.Context, playerId uint64, rodRigInfoArr []*modelRodRig.TRodRigInfo) error {
	entry := logx.NewLogEntry(ctx)
	if len(rodRigInfoArr) <= 0 {
		return fmt.Errorf("cache player:%d rod rig info is nil", playerId)
	}

	pipLine := redisx.GetGeneralCli().TxPipeline()
	values := make([]interface{}, 0)

	// 批量转换为field 列表
	for _, rodRigInfo := range rodRigInfoArr {
		if rodRigInfo == nil {
			return fmt.Errorf("cache player:%d rod rig info is nil", playerId)
		}

		values = append(values, transform.Int642Str(int64(rodRigInfo.RigId)))
		values = append(values, rodRigInfo.ToJsonStr())
	}

	pipLine.HSet(ctx, FmtRodRigInfoRdsKey(playerId), values...)
	pipLine.Expire(ctx, FmtRodRigInfoRdsKey(playerId), config.ROD_RIG_INFO_EXPIRE)
	_, err := pipLine.Exec(ctx)
	if err != nil {
		entry.Errorf("cache player:%d rod rig info:%+v error: %v", playerId, rodRigInfoArr, err)
	}

	return err
}

// ClearPlayerRodRigRds 清空玩家钓组信息
func ClearPlayerRodRigRds(ctx context.Context, playerId uint64) error {
	entry := logx.NewLogEntry(ctx)
	if err := redisx.GetGeneralCli().Del(ctx, FmtRodRigInfoRdsKey(playerId)).Err(); err != nil {
		entry.Errorf("clear player:%d rod rig info error: %v", playerId, err)
		return err
	}

	return nil
}
