package daoRodRig

import (
	"context"
	test_init "hallsrv/internal/test"
	"testing"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/mysqlx"
)

func TestQueryPlayerRodRigRds(t *testing.T) {
	test_init.InitRedisConsul()
	playerId := uint64(269)
	//redisKey := FmtRodRigInfoRdsKey(playerId)

	//redisx.GetGameCli().HSetNil(context.TODO(), redisKey)

	rodRigList, err := QueryPlayerRodRigRds(context.TODO(), playerId)
	t.Logf("rodRigList: %+v, err:%+v", rodRigList, err)
}

func TestSyncRodRigTable(t *testing.T) {
	test_init.InitRedisConsul()
	mysqlx.InitMysqlGeneralDbDevEnv()

	// 注册竿架表
	SyncRodRigTable()
}
