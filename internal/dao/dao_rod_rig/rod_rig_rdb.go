package daoRodRig

import (
	"fmt"
	"hallsrv/internal/config"
	modelRodRig "hallsrv/internal/model/model_rod_rig"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_mysql"
	"git.keepfancy.xyz/back-end/frameworks/kit/mysql"
	"github.com/ldy105cn/xorm"
	"github.com/sirupsen/logrus"
)

func getMysqlRodRigSession() (*xorm.Engine, error) {
	return mysql.GetDefaultMgr().GetMyEngine(dict_mysql.MysqlDBGeneral)
}

// SyncRodRigTable 同步数据库表单
func SyncRodRigTable() {
	engine, err := getMysqlRodRigSession()
	if engine == nil || err != nil{
		logrus.Errorf("mysql table engine is empty")                                                                                                                                                    
		return
	}

	rodRigInfo := &modelRodRig.TRodRigInfo{}
	if err := engine.Sync2(rodRigInfo); err != nil {
		logrus.Errorf("engine.Sync2(rodRigInfo) error(%v)", err)
	} else {
		logrus.Infof("sync rodRigInfo success")
	}
}

// QueryPlayerRodRigInfoRdb 查询玩家竿组信息
func QueryPlayerRodRigInfoRdb(playerId uint64) ([]*modelRodRig.TRodRigInfo, error) {
	engine, err := getMysqlRodRigSession()
	if engine == nil || err != nil {
		return nil, fmt.Errorf("mysql table engine is empty")
	}

	session := engine.Table(config.TableRodRigInfo)

	var rodRigInfos []*modelRodRig.TRodRigInfo
	// 查询玩家所有竿组信息
	err = session.Where("player_id=?", playerId).Find(&rodRigInfos)
	if err != nil {
		logrus.Errorf("session.Where(player_id = %d).Find(&rodRigInfos) error(%v)", playerId, err)
		return nil, err
	}

	return rodRigInfos, nil
}

// UpdatePlayerRodRigInfoRdb 更新玩家竿组信息
func UpdatePlayerRodRigInfoRdb(playerId uint64, rodRigInfo *modelRodRig.TRodRigInfo) error {
	if rodRigInfo == nil {
		logrus.Errorf("player:%d rodRigInfo empty", playerId)
		return fmt.Errorf("player:%d rodRigInfo empty", playerId)
	}

	engine, err := getMysqlRodRigSession()
	if engine == nil || err != nil {
		return fmt.Errorf("mysql table engine is empty")
	}

	session := engine.Table(config.TableRodRigInfo)

	queryCond := &modelRodRig.TRodRigInfo{PlayerId: playerId, RigId: rodRigInfo.RigId}

	// 是否存在
	isExist, errGet := session.Get(queryCond)
	if errGet != nil {
		logrus.Errorf("get player:%d rodRigInfo:%+v error:%+v", playerId, rodRigInfo, errGet)
		return errGet
	}

	if isExist {
		// 更新
		_, errUpdate := session.Where("player_id=? AND rig_id=?", playerId, rodRigInfo.RigId).Update(rodRigInfo)
		if errUpdate != nil {
			logrus.Errorf("update player:%d rodRigInfo:%+v error:%+v", playerId, rodRigInfo, errUpdate)
			return errUpdate
		}
	} else {
		// 查询数量 这里要重新设置table 否则会查询失败
		count, err := engine.Table(config.TableRodRigInfo).Where("player_id=?", playerId).Count()
		if err != nil {
			logrus.Errorf("failed to count rods for player:%d error:%+v", playerId, err)
			return err
		}

		// 校验是否超过最大数量
		if count >= config.MAX_ROD_RIG_COUNT {
			logrus.Errorf("player:%d has already have rig count:%d", playerId, count)
			return fmt.Errorf("player:%d has already have rig count:%d", playerId, count)
		}

		// 新增
		_, errInsert := session.Insert(rodRigInfo)
		if errInsert != nil {
			logrus.Errorf("insert player:%d rodRigInfo:%+v error:%+v", playerId, rodRigInfo, errInsert)
			return errInsert
		}
	}

	logrus.Debugf("update player:%d rodRigInfo:%+v success", playerId, rodRigInfo)

	return nil
}

// DelPlayerRodRigInfoRdb 删除竿组
func DelPlayerRodRigInfoRdb(playerId uint64, rigId int32) error {
	engine, err := getMysqlRodRigSession()
	if engine == nil || err != nil {
		return fmt.Errorf("mysql table engine is empty")
	}

	session := engine.Table(config.TableRodRigInfo)

	// 硬删除
	_, errDel := session.Where("player_id=? AND rig_id=?", playerId, rigId).Unscoped().Delete(&modelRodRig.TRodRigInfo{})
	if errDel != nil {
		logrus.Errorf("delete player:%d rodRigInfo:%+v error:%+v", playerId, rigId, errDel)
		return errDel
	}

	logrus.Debugf("del player:%d rodRigInfo:%+v success", playerId, rigId)
	
	return nil
}
