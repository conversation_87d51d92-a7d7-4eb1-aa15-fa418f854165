package dao_trip_rod

import (
	"context"
	"errors"
	"fmt"
	"hallsrv/internal/config"
	"hallsrv/internal/model/model_bag"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
	"github.com/go-redis/redis/v8"
)

func redisKey(playerId uint64) string {
	return fmt.Sprintf(config.RDS_KEY_TRIP_ROD, playerId)
}

func getRedisCli() *redisx.Client {
	return redisx.GetGeneralCli()
}

func getAllCache(ctx context.Context, playerId uint64) ([]*model_bag.TTripRodGroup, error) {
	key := redisKey(playerId)
	hashMap, err := getRedisCli().HGetAllWithNil(ctx, key).Result()
	if err != nil {
		if errors.Is(err, redisx.Empty) {
			return []*model_bag.TTripRodGroup{}, nil
		}
	}
	// 无数据，触发降级查询
	if len(hashMap) == 0 {
		return nil, redis.Nil
	}
	rtn := make([]*model_bag.TTripRodGroup, 0)
	for _, hash := range hashMap {
		data := &model_bag.TTripRodGroup{}
		if err := data.FromHash(hash); err != nil {
			return nil, err
		}
		rtn = append(rtn, data)
	}

	return rtn, nil
}

func setCache(ctx context.Context, playerId uint64, data []*model_bag.TTripRodGroup) error {
	key := redisKey(playerId)

	args := make([]interface{}, 0)
	for _, value := range data {
		args = append(args, value.Id, value.ToHash())
	}

	pipe := getRedisCli().Pipeline()
	pipe.HSet(ctx, key, args...)
	pipe.Expire(ctx, key, config.TRIP_ROD_EXPIRE)
	_, err := pipe.Exec(ctx)
	if err != nil {
		return err
	}
	return nil
}

func delCache(ctx context.Context, playerId uint64) error {
	key := redisKey(playerId)
	_, err := getRedisCli().Del(ctx, key).Result()
	return err
}
