package dao_trip_rod

import (
	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_mysql"
	"git.keepfancy.xyz/back-end/frameworks/kit/mysql"
	"github.com/ldy105cn/xorm"
	"github.com/sirupsen/logrus"
	"hallsrv/internal/model/model_bag"
)

func GetSqlEngine() (*xorm.Engine, error) {
	engine, err := mysql.GetDefaultMgr().GetMyEngine(dict_mysql.MysqlDBGeneral)
	// 控制打印sql语句
	// engine.Logger().ShowSQL(true)
	// engine.Logger().SetLevel(core.LOG_INFO)
	// engine.SetLogger(engine.Logger())
	return engine, err
}

func del(session *xorm.Session, playerId uint64, id int32) error {
	// 双库双删
	var err error
	queryGroup := &model_bag.TTripRodGroup{
		PlayerId: playerId,
		Id:       id,
	}
	_, err = session.Delete(queryGroup)
	if err != nil {
		return err
	}
	queryRod := &model_bag.TTripRod{
		PlayerId: playerId,
		Id:       id,
	}
	_, err = session.Delete(queryRod)
	if err != nil {
		return err
	}

	return nil
}

// 放入背包
func put(session *xorm.Session, playerId uint64, id, bagIndex int32) (oldRigId int32, err error) {
	oldRigId = 0
	oldRig := model_bag.TTripRodGroup{}
	has, err := session.Where("player_id = ? AND bag_index = ?", playerId, bagIndex).Get(&oldRig)
	if err != nil {
		return
	}
	if has {
		// 事务 先卸下对应位置的杆组
		_, err = session.
			Table(new(model_bag.TTripRodGroup)).
			Where("player_id = ? AND bag_index = ?", playerId, bagIndex).
			Update(map[string]interface{}{"bag_index": 0})

		if err != nil {
			return
		}
		oldRigId = oldRig.Id
	}
	// 装上
	_, err = session.
		Table(new(model_bag.TTripRodGroup)).
		Where("player_id = ? AND id = ?", playerId, id).
		Update(map[string]interface{}{"bag_index": bagIndex})

	return
}

// 卸下
func unload(session *xorm.Session, playerId uint64, id int32) error {
	_, err := session.
		Table(new(model_bag.TTripRodGroup)).
		Where("player_id = ? AND id = ?", playerId, id).
		Update(map[string]interface{}{"bag_index": 0})
	return err
}

// 卸下
func unloadAll(session *xorm.Session, playerId uint64) error {
	_, err := session.
		Table(new(model_bag.TTripRodGroup)).
		Where("player_id = ?", playerId).
		Update(map[string]interface{}{"bag_index": 0})
	return err
}

func update(session *xorm.Session, change *model_bag.TTripRodGroup) error {
	// 检查group
	old := &model_bag.TTripRodGroup{
		PlayerId: change.PlayerId,
		Id:       change.Id,
	}
	has, err := session.Where("player_id = ? AND id = ?", change.PlayerId, change.Id).Get(old)
	if err != nil {
		return err
	}
	if has {
		_, err := session.Where("player_id = ? AND id = ?", change.PlayerId, change.Id).Update(change)
		if err != nil {
			return err
		}
		// TODO 减少替换量
		// 旧的全删
		query := &model_bag.TTripRod{
			PlayerId: change.PlayerId,
			Id:       change.Id,
		}
		_, err = session.Delete(query)
		if err != nil {
			return err
		}
		// 新的全加
		_, err = session.Insert(change.ToRodList())
		if err != nil {
			return err
		}

		return nil
	} else {
		_, err := session.Insert(change)
		if err != nil {
			return err
		}
		_, err = session.Insert(change.ToRodList())
		if err != nil {
			return err
		}

		return nil
	}
}

func find(query *model_bag.TTripRodGroup) ([]*model_bag.TTripRodGroup, error) {
	engine, err := GetSqlEngine()
	if err != nil {
		return nil, err
	}
	rtn := make([]*model_bag.TTripRodGroup, 0)
	err = engine.Find(&rtn, query)
	if err != nil {
		return nil, err
	}

	return rtn, nil
}

func GetPlayerMaxId(playerId uint64) (int32, error) {
	engine, err := GetSqlEngine()
	if err != nil {
		return 0, err
	}

	rtn := model_bag.TTripRodGroup{}
	_, err = engine.Select("max(id) as id").Where("player_id = ?", playerId).Get(&rtn)
	if err != nil {
		return 0, err
	}

	return rtn.Id, nil
}

func insert(session *xorm.Session, change *model_bag.TTripRodGroup) error {
	_, err := session.Insert(change)
	if err != nil {
		return err
	}
	logrus.Infof("insert rod group success change=%+v", *change)
	// 赋值新id
	for i := range change.Data {
		change.Data[i].Id = change.Id
	}
	_, err = session.Insert(change.ToRodList())
	if err != nil {
		return err
	}
	return nil
}
