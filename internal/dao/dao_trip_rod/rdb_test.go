package dao_trip_rod

import (
	"hallsrv/internal/model/model_bag"
	test_init "hallsrv/internal/test"
	"testing"
)

func TestSync(t *testing.T) {
	test_init.InitSql()
	engine, err := GetSqlEngine()
	if err != nil {
		t.Fatalf("sql engine error:%+v", err)
	}
	// engine.DropTables(new(model_bag.TTripRodGroup), new(model_bag.TTripRod))
	engine.Sync2(new(model_bag.TTripRodGroup), new(model_bag.TTripRod))
}

func TestFind(t *testing.T) {
	test_init.InitSql()

	query := &model_bag.TTripRodGroup{
		Id:       1,
		PlayerId: 1,
	}

	rtn, err := find(query)
	if err != nil {
		t.Fatalf("fail %+v", err)
	}
	t.Logf("print %+v", rtn)
}

func testData(playerId uint64, id int32) *model_bag.TTripRodGroup {
	return &model_bag.TTripRodGroup{
		Id:       id,
		PlayerId: uint64(playerId),
		Name:     "rod_group_1",
		Data: map[int32]*model_bag.TTripRod{
			1: {
				Id:         id,
				PlayerId:   playerId,
				ItemId:     1,
				InstanceId: "rod_1",
				SitId:      1,
			},
			2: {
				Id:         id,
				PlayerId:   playerId,
				ItemId:     1,
				InstanceId: "rod_2",
				SitId:      2,
			},
		},
	}
}

func TestUpdate(t *testing.T) {
	test_init.InitSql()
	engine, _ := GetSqlEngine()
	session := engine.NewSession()
	defer session.Close()
	session.Begin()

	err := update(session, testData(5, 1))
	if err != nil {
		session.Rollback()
		t.Fatalf("fail %+v", err)
	}
	session.Commit()
}

func TestDel(t *testing.T) {
	test_init.InitSql()
	engine, _ := GetSqlEngine()
	session := engine.NewSession()
	defer session.Close()
	session.Begin()

	err := del(session, 5, 1)
	if err != nil {
		session.Rollback()
		t.Fatalf("fail %+v", err)
	}
	session.Commit()
}
