package dao_trip_rod

import (
	"context"
	"errors"
	"fmt"
	"hallsrv/internal/model/model_bag"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
	"github.com/ldy105cn/xorm"
)

func Get(ctx context.Context, playerId uint64, id int32) (*model_bag.TTripRodGroup, error) {
	all, err := GetALL(ctx, playerId)
	if err != nil {
		return nil, err
	}
	for _, one := range all {
		if one.Id == id {
			return one, nil
		}
	}
	return nil, protox.CodeError(commonPB.ErrCode_ERR_NOT_EXIST, fmt.Sprintf("trip_rod:%+v not exist", id))
}

func GetALL(ctx context.Context, playerId uint64) ([]*model_bag.TTripRodGroup, error) {
	// 查缓存
	cacheArr, err := getAllCache(ctx, playerId)
	if errors.Is(err, redisx.Empty) {
		return []*model_bag.TTripRodGroup{}, nil
	}

	// 降级
	if err != nil {
		cacheArr, err = cacheDownGrade(ctx, playerId)
		if err != nil {
			return nil, err
		}
		return cacheArr, nil
	}

	return cacheArr, nil
}

// Update 更新杆包缓存
func Update(ctx context.Context, session *xorm.Session, change *model_bag.TTripRodGroup) (err error) {
	entry := logx.NewLogEntry(ctx)
	if change == nil {
		return protox.PB2Error(commonPB.ErrCode_ERR_BAD_PARAM)
	}

	err = update(session, change)
	if err != nil {
		return err
	}

	delErr := delCache(ctx, change.PlayerId)
	if delErr != nil {
		entry.Warnf("bagRod delCache err:%v playerId:%+v", delErr, change.PlayerId)
	}

	return nil
}

// Insert 插入新钓组
func Insert(ctx context.Context, session *xorm.Session, change *model_bag.TTripRodGroup) (err error) {
	entry := logx.NewLogEntry(ctx)
	if change == nil {
		return protox.PB2Error(commonPB.ErrCode_ERR_BAD_PARAM)
	}

	err = insert(session, change)
	if err != nil {
		return err
	}

	delErr := delCache(ctx, change.PlayerId)
	if delErr != nil {
		entry.Warnf("bagRod delCache err:%v playerId:%+v", delErr, change.PlayerId)
	}

	return nil
}

// Del 删除杆包
func Del(ctx context.Context, session *xorm.Session, playerId uint64, id int32) error {
	entry := logx.NewLogEntry(ctx)
	var err error
	err = del(session, playerId, id)
	if err != nil {
		return err
	}
	delErr := delCache(ctx, playerId)
	if delErr != nil {
		entry.Warnf("bagRod delCache err:%v playerId:%+v", delErr, playerId)
	}

	return nil
}

// Put 杆组放入背包
func Put(ctx context.Context, session *xorm.Session, playerId uint64, id, bagIndex int32) (oldRigId int32, err error) {
	entry := logx.NewLogEntry(ctx)

	oldRigId, err = put(session, playerId, id, bagIndex)
	if err != nil {
		return
	}
	delErr := delCache(ctx, playerId)
	if delErr != nil {
		entry.Warnf("bagRod delCache err:%v playerId:%+v", delErr, playerId)
	}

	return
}

// Unload 卸下背包的杆组
func Unload(ctx context.Context, session *xorm.Session, playerId uint64, id int32) error {
	entry := logx.NewLogEntry(ctx)
	var err error
	err = unload(session, playerId, id)
	if err != nil {
		return err
	}
	delErr := delCache(ctx, playerId)
	if delErr != nil {
		entry.Warnf("bagRod delCache err:%v playerId:%+v", delErr, playerId)
	}

	return nil
}

// UnloadAll 卸下背包的所有杆组
func UnloadAll(ctx context.Context, session *xorm.Session, playerId uint64) error {
	entry := logx.NewLogEntry(ctx)
	var err error
	err = unloadAll(session, playerId)
	if err != nil {
		return err
	}
	delErr := delCache(ctx, playerId)
	if delErr != nil {
		entry.Warnf("bagRod delCache err:%v playerId:%+v", delErr, playerId)
	}

	return nil
}

// 降级查询
func cacheDownGrade(ctx context.Context, playerId uint64) ([]*model_bag.TTripRodGroup, error) {
	query := &model_bag.TTripRodGroup{
		PlayerId: playerId,
	}
	arr, err := find(query)
	if err != nil {
		return nil, err
	}

	if len(arr) > 0 {
		_ = setCache(ctx, playerId, arr)
	} else {
		key := redisKey(playerId)
		getRedisCli().HSetNil(ctx, key)
	}
	return arr, nil
}
