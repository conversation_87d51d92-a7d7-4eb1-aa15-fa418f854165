package dao_cdk

import (
	"context"
	"errors"
	"fmt"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
	"hallsrv/internal/config"
	"hallsrv/internal/model/model_cdk"
)

func GetRedisCli() *redisx.Client {
	return redisx.GetGeneralCli()
}

// GetCdkInfoFromCache 从缓存中获取CDK信息
func GetCdkInfoFromCache(ctx context.Context, cdkCode string) (*model_cdk.CdkBatchRecordResult, error) {
	key := config.CdkInfoKey(cdkCode)
	data, err := GetRedisCli().Get(ctx, key).Result()
	if err != nil {
		return nil, err
	}

	// 使用model层的反序列化方法
	return model_cdk.NewCdkBatchRecordResultFromJSON(data)
}

// SetCdkInfoToCache 将CDK信息存入缓存
func SetCdkInfoToCache(ctx context.Context, info *model_cdk.CdkBatchRecordResult) error {
	if info == nil || info.Cdk == "" {
		return errors.New("cdk info is nil")
	}

	entry := logx.NewLogEntry(ctx)

	// 使用model层的序列化方法
	data, err := info.ToJSON()
	if err != nil {
		return err
	}

	// 存入缓存
	key := config.CdkInfoKey(info.Cdk)
	err = GetRedisCli().Set(ctx, key, data, config.CDK_CACHE_EXPIRE).Err()
	if err != nil {
		entry.Errorf("set cache failed: cdk=%s err=%v", info.Cdk, err)
		return err
	}

	return nil
}

// DeleteCdkInfoFromCache 从缓存中删除CDK信息
func DeleteCdkInfoFromCache(ctx context.Context, cdkCode string) error {
	entry := logx.NewLogEntry(ctx)

	key := config.CdkInfoKey(cdkCode)
	_, err := GetRedisCli().Del(ctx, key).Result()
	if err != nil {
		entry.Errorf("delete cache failed: cdk=%s err=%v", cdkCode, err)
		return err
	}

	return nil
}

// DeleteBatchCdkCaches 批量删除指定批次下所有CDK的缓存
func DeleteBatchCdkCaches(ctx context.Context, batchID uint64) error {

	// 查询该批次下的所有CDK码
	db, err := getDbRunner()
	if err != nil {
		return err
	}

	var cdkCodes []string
	err = db.Table(config.TableCDKRecord).
		Where("batch_id = ?", batchID).
		Cols("cdk").
		Find(&cdkCodes)
	if err != nil {
		return fmt.Errorf("failed to query cdk codes for batch %d: %w", batchID, err)
	}

	if len(cdkCodes) == 0 {
		return nil
	}

	// 批量删除缓存
	keys := make([]string, 0, len(cdkCodes))
	for _, cdkCode := range cdkCodes {
		keys = append(keys, config.CdkInfoKey(cdkCode))
	}

	_, err = GetRedisCli().Del(ctx, keys...).Result()
	if err != nil {
		return fmt.Errorf("failed to delete batch caches for batch %d: %w", batchID, err)
	}
	return nil
}
