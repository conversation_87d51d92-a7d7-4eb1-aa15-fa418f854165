package daoItemHeap

import (
	"context"
	"fmt"
	"hallsrv/internal/config"
	modelItem "hallsrv/internal/model/model_item"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
	"git.keepfancy.xyz/back-end/frameworks/lib/transform"
)

func FmtItemHeapDurableRdsKey(playerId uint64) string {
	return fmt.Sprintf(config.RDS_KEY_ITEM_HEAP_DURABLE, playerId)
}

// QueryItemHeapDurableRds 查询指定道具堆耐久信息
func QueryItemHeapDurableRds(ctx context.Context, playerId uint64, itemId int64) (*modelItem.TItemHeapDurable, error) {
	entry := logx.NewLogEntry(ctx)
	
	key := FmtItemHeapDurableRdsKey(playerId)

	durable, err := redisx.GetGeneralCli().HGetWithNil(ctx, key, transform.Int642Str(itemId)).Result()

	if err != nil {
		entry.Warnf("query player:%d item heap:%d warn:%+v", playerId, itemId, err)
		return nil, err
	}

	itemHeapDurable := modelItem.NewItemHeapDurableFromJsonStr(durable)
	if itemHeapDurable == nil {
		entry.Warnf("query player:%d item heap:%d durable error: durable info is nil", playerId, itemId)
		return nil, fmt.Errorf("durable info is nil")
	}
	
	return itemHeapDurable, nil
}

// QueryAllItemHeapDurableRds 查询玩家所有道具堆耐久信息
func QueryAllItemHeapDurableRds(ctx context.Context, playerId uint64) ([]*modelItem.TItemHeapDurable, error) {
	entry := logx.NewLogEntry(ctx)
	
	key := FmtItemHeapDurableRdsKey(playerId)
	durableArr, err := redisx.GetGeneralCli().HGetAllWithNil(ctx, key).Result()
	if err != nil {
		entry.Warnf("query player:%d item heap durable warn:%+v", playerId, err)
		return nil, err
	}

	// 解析出数据
	durableList := make([]*modelItem.TItemHeapDurable, len(durableArr))
	for _, v := range durableArr {
		durableInfo := modelItem.NewItemHeapDurableFromJsonStr(v)
		if durableInfo == nil {
			continue
		}

		durableList = append(durableList, durableInfo)
	}
	
	return durableList, nil 
}

// UpdateAllItemHeapDurableRds 更新玩家所有道具堆耐久信息
func UpdateAllItemHeapDurableRds(ctx context.Context, playerId uint64, durableList []*modelItem.TItemHeapDurable) error {
	entry := logx.NewLogEntry(ctx)
	key := FmtItemHeapDurableRdsKey(playerId)

	pipline := redisx.GetGeneralCli().TxPipeline()
	rdsHashArr := make([]interface{}, 0, len(durableList)*2)

	for _, v := range durableList {
		rdsHashArr = append(rdsHashArr, v.ItemId)
		rdsHashArr = append(rdsHashArr, v.ToJsonStr())
	}

	pipline.HSet(ctx, key, rdsHashArr...)
	pipline.Expire(ctx, key, config.ITEM_HEAP_DURABLE_EXPIRE)

	_, err := pipline.Exec(ctx)
	if err != nil {
		entry.Errorf("update player:%d item heap durable error:%+v", playerId, err)
		return err
	}

	return nil
}

// ClearItemHeapDurableRds 清空玩家堆耐久信息
func ClearItemHeapDurableRds(ctx context.Context, playerId uint64) error {
	entry := logx.NewLogEntry(ctx)
	
	key := FmtItemHeapDurableRdsKey(playerId)

	_, err := redisx.GetGeneralCli().Del(ctx, key).Result()
	if err != nil {
		entry.Errorf("clear player:%d item heap durable error:%+v", playerId, err)
		return err
	}

	return nil
}