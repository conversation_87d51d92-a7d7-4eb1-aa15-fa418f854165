package daoItemHeap

import (
	"context"
	"fmt"
	"hallsrv/internal/config"
	modelItem "hallsrv/internal/model/model_item"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_mysql"
	"git.keepfancy.xyz/back-end/frameworks/kit/mysql"
	"github.com/ldy105cn/xorm"
	"github.com/sirupsen/logrus"
)

func getMysqlGeneralSession() (*xorm.Engine, error) {
	return mysql.GetDefaultMgr().GetMyEngine(dict_mysql.MysqlDBGeneral)
}

// SyncItemHeapDurableTable 同步数据库表单
func SyncItemHeapDurableTable() {
	engine, err := getMysqlGeneralSession()
	if engine == nil || err != nil{
		logrus.Errorf("mysql table engine is empty")                                                                                                                                                    
		return
	}

	itemHeapDurable := &modelItem.TItemHeapDurable{}
	if err := engine.Sync2(itemHeapDurable); err != nil {
		logrus.Errorf("engine.Sync2(itemHeapDurable) error(%v)", err)
	} else {
		logrus.Infof("sync itemHeapDurable success")
	}
}

// QueryItemHeapDurableRdb 查询玩家指定道具堆的耐久数据
func QueryItemHeapDurableRdb(ctx context.Context, playerId uint64, itemId int64) (*modelItem.TItemHeapDurable, error) {
	engine, err := getMysqlGeneralSession()
	if engine == nil || err != nil {
		return nil, fmt.Errorf("mysql table engine is empty")
	}

	session := engine.Table(config.TableItemHeapDurable)

	itemHeapDurableInfo := &modelItem.TItemHeapDurable{}
	// 查询玩家所有竿组信息
	exist, err := session.Where("player_id=? and item_id=?", playerId, itemId).Limit(1, 0).Get(itemHeapDurableInfo)
	if err != nil {
		logrus.Errorf("session.Where(player_id = %d).Find(itemHeapDurableInfo) error(%+v)", playerId, err)
		return nil, err
	} else if !exist {
		logrus.Warnf("session.Where(player_id = %d).Find(itemHeapDurableInfo) not exist", playerId)
		return nil, err
	}

	return itemHeapDurableInfo, nil
}

// QueryAllItemHeapDurableRdb 查询玩家所有道具堆耐久数据
func QueryAllItemHeapDurableRdb(ctx context.Context, playerId uint64) ([]*modelItem.TItemHeapDurable, error) {

	engine, err := getMysqlGeneralSession()
	if engine == nil || err != nil {
		return nil, fmt.Errorf("mysql table engine is empty")
	}

	session := engine.Table(config.TableItemHeapDurable)

	var itemHeapDurableArr []*modelItem.TItemHeapDurable
	// 查询玩家所有竿组信息
	err = session.Where("player_id=?", playerId).Find(&itemHeapDurableArr)
	if err != nil {
		logrus.Errorf("session.Where(player_id = %d).Find(&itemHeapDurableArr) error(%+v)", playerId, err)
		return nil, err
	}

	return itemHeapDurableArr, nil
}

// UpdateItemHeapDurableRdb 更新玩家指定道具堆的耐久数据
func UpdateItemHeapDurableRdb(ctx context.Context, playerId uint64, itemId int64, durable int32) error {
	engine, err := getMysqlGeneralSession()
	if engine == nil || err != nil {
		return fmt.Errorf("mysql table engine is empty")
	}

	session := engine.Table(config.TableItemHeapDurable)

	queryCond := &modelItem.TItemHeapDurable{PlayerId: playerId, ItemId: itemId}

	// 是否存在
	isExist, errGet := session.Get(queryCond)
	if errGet != nil {
		logrus.Errorf("get player:%d item:%d error:%+v", playerId, itemId, errGet)
		return errGet
	}

	// 更新参数
	itemHeapDurable := &modelItem.TItemHeapDurable{PlayerId: playerId, ItemId: itemId, DurablePer: durable}

	if isExist {
		// 更新
		_, errUpdate := session.Where("player_id=? AND item_id=?", playerId, itemId).Update(itemHeapDurable)
		if errUpdate != nil {
			logrus.Errorf("update player:%d itemHeapDurable:%+v error:%+v", playerId, itemHeapDurable, errUpdate)
			return errUpdate
		}
	} else {
		// 新增
		_, errInsert := session.Insert(itemHeapDurable)
		if errInsert != nil {
			logrus.Errorf("insert player:%d itemHeapDurable:%+v error:%+v", playerId, itemHeapDurable, errInsert)
			return errInsert
		}
	}

	return nil
}