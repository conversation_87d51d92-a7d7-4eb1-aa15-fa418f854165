package dao_trip_bag

import (
	"hallsrv/internal/model/model_bag"
	test_init "hallsrv/internal/test"
	"testing"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
)

func TestSync(t *testing.T) {
	test_init.InitSql()
	engine, err := getSqlEngine()
	if err != nil {
		t.Fatalf("sql engine error:%+v", err)
	}
	engine.DropTables(new(model_bag.TTripBag))
	engine.Sync2(new(model_bag.TTripBag))
}

func TestStore(t *testing.T) {
	type args struct {
		opt        commonPB.ITEM_OPERATION
		changeList []*model_bag.TTripBag
	}
	type want struct {
		name    string
		args    args
		wantErr bool
	}
	tests := []*want{
		// TODO: Add test cases.
		&want{name: "testNotItem", wantErr: true, args: args{
			opt: commonPB.ITEM_OPERATION_IO_REDUCE,
			changeList: []*model_bag.TTripBag{
				&model_bag.TTripBag{PlayerId: 1, BagType: 1, ItemId: 1, Count: 1},
			},
		}},
		&want{name: "testAdd", wantErr: false, args: args{
			opt: commonPB.ITEM_OPERATION_IO_ADD,
			changeList: []*model_bag.TTripBag{
				&model_bag.TTripBag{PlayerId: 2, BagType: 1, ItemId: 2, Count: 1},
			},
		}},
		&want{name: "testAddAdd", wantErr: false, args: args{
			opt: commonPB.ITEM_OPERATION_IO_ADD,
			changeList: []*model_bag.TTripBag{
				&model_bag.TTripBag{PlayerId: 2, BagType: 1, ItemId: 2, Count: 1},
			},
		}},
		&want{name: "testAddAfterReduce", wantErr: false, args: args{
			opt: commonPB.ITEM_OPERATION_IO_REDUCE,
			changeList: []*model_bag.TTripBag{
				&model_bag.TTripBag{PlayerId: 2, BagType: 1, ItemId: 2, Count: 1},
			},
		}},
		&want{name: "testReduce2Not", wantErr: false, args: args{
			opt: commonPB.ITEM_OPERATION_IO_REDUCE,
			changeList: []*model_bag.TTripBag{
				&model_bag.TTripBag{PlayerId: 2, BagType: 1, ItemId: 2, Count: 1},
			},
		}},
	}
	test_init.InitSql()
	engine, err := getSqlEngine()
	if err != nil {
		t.Fatalf("sql engine error:%+v", err)
	}
	session := engine.NewSession()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if _, err := store(session, tt.args.opt, tt.args.changeList); (err != nil) != tt.wantErr {
				t.Errorf("Store() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
