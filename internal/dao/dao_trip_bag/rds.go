package dao_trip_bag

import (
	"context"
	"errors"
	"fmt"
	"hallsrv/internal/config"
	"hallsrv/internal/model/model_bag"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
	"github.com/go-redis/redis/v8"
)

// redisKey
func redisKey[BagType ~int32](playerId uint64, bagType BagType) string {
	return fmt.Sprintf(config.RDS_KEY_TRIP_BAG, playerId, bagType)
}

func getRedisCli() *redisx.Client {
	return redisx.GetGeneralCli()
}

func getCache(ctx context.Context, playerId uint64, bagType int32, instanceId string) {

}

func getAllCache(ctx context.Context, playerId uint64, bagType int32) ([]*model_bag.TTripBag, error) {
	key := redisKey(playerId, bagType)
	hashMap, err := getRedisCli().HGetAllWithNil(ctx, key).Result()
	if err != nil {
		if errors.Is(err, redisx.Empty) {
			return []*model_bag.TTripBag{}, nil
		}
		return nil, err
	}
	// 任务数据为空
	if len(hashMap) == 0 {
		return nil, redis.Nil
	}
	rtn := make([]*model_bag.TTripBag, 0)
	for _, hash := range hashMap {
		data := model_bag.NewTripBag()
		err := data.FormHash(hash)
		if err != nil {
			return nil, err
		}
		rtn = append(rtn, data)
	}
	return rtn, nil
}

func setCache(ctx context.Context, playerId uint64, bagType int32, changeList []*model_bag.TTripBag) error {
	key := redisKey(playerId, bagType)

	args := make([]interface{}, 0)
	for _, value := range changeList {
		args = append(args, value.InstanceId, value.ToHash())
	}

	pipe := getRedisCli().Pipeline()
	pipe.HSet(ctx, key, args...)
	// 只保留一段短有效期
	pipe.Expire(ctx, key, config.TRIP_BAG_EXPIRE)
	_, err := pipe.Exec(ctx)
	if err != nil {
		return err
	}
	return err
}

func delCache(ctx context.Context, playerId uint64, bagType int32) error {
	key := redisKey(playerId, bagType)
	_, err := getRedisCli().Del(ctx, key).Result()
	return err
}
