package dao_trip_bag

import (
	"errors"
	"fmt"
	"hallsrv/internal/model/model_bag"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_mysql"
	"git.keepfancy.xyz/back-end/frameworks/kit/mysql"
	"github.com/ldy105cn/xorm"
)

func getSqlEngine() (*xorm.Engine, error) {
	engine, err := mysql.GetDefaultMgr().GetMyEngine(dict_mysql.MysqlDBGeneral)
	// 控制打印sql语句
	engine.Logger().ShowSQL(true)
	engine.SetLogger(engine.Logger())
	return engine, err
}

// 数据库存车处
func store(session *xorm.Session, opt commonPB.ITEM_OPERATION, changeList []*model_bag.TTripBag) ([]*model_bag.TTripBag, error) {
	updateList := make([]*model_bag.TTripBag, 0)
	for _, change := range changeList {
		condBean := change.DBBean()
		has, err := session.Get(condBean)
		if err != nil {
			return nil, err
		}

		switch opt {
		case commonPB.ITEM_OPERATION_IO_ADD:
			if has {
				// 有，则增加
				_, err := session.Incr("count", change.Count).Update(change, change.DBBean())
				if err != nil {
					return nil, err
				}
				condBean.Count += change.Count
				updateList = append(updateList, condBean)
			} else {
				// 无，则插入
				_, err := session.Insert(change)
				if err != nil {
					return nil, err
				}
				updateList = append(updateList, change)
			}
		case commonPB.ITEM_OPERATION_IO_REDUCE:
			// 数值检查
			if change.Count <= 0 {
				return nil, errors.New("invalid count")
			}
			if has {
				if condBean.Count > change.Count {
					// 有且数量足够
					effectNum, err := session.Decr("count", change.Count).Where("count > ?", change.Count).Update(change, change.DBBean())
					if err != nil {
						return nil, err
					}
					if effectNum == 0 {
						return nil, fmt.Errorf("item count not enough id:[%d:%s]", change.ItemId, change.InstanceId)
					}
				} else if condBean.Count == change.Count {
					// 数量相同则删除
					effectNum, err := session.Where("count = ?", change.Count).Delete(change.DBBean())
					if err != nil {
						return nil, err
					}
					if effectNum == 0 {
						return nil, fmt.Errorf("trip bag delete fail id:[%d:%s]", change.ItemId, change.InstanceId)
					}
					condBean.Count -= change.Count
					updateList = append(updateList, condBean)
				} else {
					// 扣的数量比现有的多
					return nil, errors.New("invalid count")
				}
			} else {
				return nil, errors.New("deduct item not exist")
			}
		default:
			return nil, errors.New("invalid operation")
		}

	}

	return updateList, nil
}

// 数据库查询
func find(playerId uint64, query *model_bag.TTripBag) ([]*model_bag.TTripBag, error) {
	// query 非空字段为查询字段
	engine, err := getSqlEngine()
	if err != nil {
		return nil, err
	}

	list := make([]*model_bag.TTripBag, 0)

	err = engine.Find(&list, query)
	if err != nil {
		return nil, err
	}

	return list, nil
}
