package dao_trip_bag

import (
	"context"
	"errors"
	"fmt"
	"hallsrv/internal/model/model_bag"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_mysql"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
	"git.keepfancy.xyz/back-end/frameworks/kit/mysql"
	"github.com/ldy105cn/xorm"
)

func GetMysqlEngine() (*xorm.Engine, error) {
	return mysql.GetDefaultMgr().GetMyEngine(dict_mysql.MysqlDBGeneral)
}

// Get 单个查询
func Get() {

}

// GetAll 全量查询
func GetAll(ctx context.Context, playerId uint64, bagType int32) ([]*model_bag.TTripBag, error) {
	if bagType == 0 {
		return nil, fmt.Errorf("unknown bag Type:%+v", bagType)
	}
	cacheArr, err := getAllCache(ctx, playerId, bagType)
	if errors.Is(err, redisx.Empty) {
		return []*model_bag.TTripBag{}, nil
	}
	if err != nil {
		// 降级保护
		cacheArr, err = cacheDownGrade(ctx, playerId, bagType)
		if err != nil {
			return nil, err
		}
		return cacheArr, nil
	}
	// 空也返回
	return cacheArr, nil
}

// 降级读取
func cacheDownGrade(ctx context.Context, playerId uint64, bagType int32) ([]*model_bag.TTripBag, error) {
	query := &model_bag.TTripBag{
		PlayerId: playerId,
		BagType:  bagType,
	}
	arr, err := find(playerId, query)
	if err != nil {
		return nil, err
	}
	if len(arr) > 0 {
		//  写入缓存
		_ = setCache(ctx, playerId, bagType, arr)
	} else {
		// 设置空标记拦截
		key := redisKey(playerId, bagType)
		getRedisCli().HSetNil(ctx, key)
	}
	return arr, nil
}

// Modify 修改
func Modify(ctx context.Context, session *xorm.Session, opt commonPB.ITEM_OPERATION, changeList []*model_bag.TTripBag) (updateList []*model_bag.TTripBag, err error) {
	entry := logx.NewLogEntry(ctx)
	if len(changeList) <= 0 {
		return nil, protox.PB2Error(commonPB.ErrCode_ERR_BAD_PARAM)
	}
	updateList, err = store(session, opt, changeList)
	if err != nil {
		return nil, err
	}
	change := changeList[0]
	playerId := change.PlayerId
	bagType := change.BagType
	// 不删除，只更新可能有不一致的问题
	// 1. sql更新  redis 失效
	// 2. redis 恢复，两者不一致
	delErr := delCache(ctx, playerId, bagType)
	if delErr != nil {
		entry.Warnf("bagTrip delCache err:%v playerId:%+v", delErr, playerId)
	}

	// err = setCache(ctx, playerId, bagType, changeList)
	// if err != nil {
	// 	return err
	// }

	return updateList, nil
}

func ModifyFromItemBase(ctx context.Context, session *xorm.Session, opt commonPB.ITEM_OPERATION, bagType commonPB.TRIP_BAG_TYPE, changeList []*commonPB.ItemBase) ([]*model_bag.TTripBag, error) {
	newChangeList := make([]*model_bag.TTripBag, 0)
	for _, item := range changeList {
		new := model_bag.NewTripBag()
		new.FromItemBase(item)
		new.BagType = int32(bagType)

		newChangeList = append(newChangeList, new)
	}

	return Modify(ctx, session, opt, newChangeList)
}
