package daoGoods

import (
	"context"
	"errors"
	"fmt"
	"hallsrv/internal/config"
	modelStore "hallsrv/internal/model/model_store"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
	"git.keepfancy.xyz/back-end/frameworks/lib/transform"
	"github.com/go-redis/redis/v8"
)

func fmtPlayerGoodsBuyRdsKey(playerId uint64) string {
	return fmt.Sprintf(config.RDS_KEY_PLAYER_GOODS_BUY, playerId)
}

// QueryPlayerGoodsBuyInfo 查询指定玩家购买商品信息
func QueryPlayerGoodsBuyInfo(ctx context.Context, playerId uint64, goodsId int64) (*modelStore.GoodsBuyInfo, error) {
	entry := logx.NewLogEntry(ctx)
	if playerId == 0 || goodsId <= 0 {
		entry.Warnf("get player:%d or goods:%d info error", playerId, goodsId)
		return nil, fmt.Errorf("get player:%d or goods:%d info error", playerId, goodsId)
	}

	goodsInfoStr, err := redisx.GetPlayerCli().HGet(ctx, fmtPlayerGoodsBuyRdsKey(playerId), transform.Int642Str(goodsId)).Result()

	if errors.Is(err, redis.Nil) {
		return modelStore.NewGoodsBuyInfo(goodsId), nil
	}

	if err != nil {
		entry.Errorf("get player:%d goods info error, %s", playerId, err)
		return nil, err
	}

	goodsInfo := modelStore.NewGoodsBuyInfoJson(goodsInfoStr)
	if goodsInfo == nil {
		entry.Warnf("get player:%d goods info error, %v", playerId, goodsInfoStr)
		return nil, fmt.Errorf("get player:%d goods info error, %v", playerId, goodsInfoStr)
	}

	return goodsInfo, nil
}

// QueryPlayerAllGoodsBuyInfo 查询玩家所有购买商品信息
func QueryPlayerAllGoodsBuyInfo(ctx context.Context, playerId uint64) (map[int64]*modelStore.GoodsBuyInfo, error) {
	entry := logx.NewLogEntry(ctx)
	if playerId == 0 {
		entry.Warnf("get player:%d goods info error, player id is zero", playerId)
		return nil, fmt.Errorf("get player:%d goods info error, player id is zero", playerId)
	}

	goodsInfoMap, err := redisx.GetPlayerCli().HGetAll(ctx, fmtPlayerGoodsBuyRdsKey(playerId)).Result()

	if errors.Is(err, redis.Nil) {
		return nil, nil
	}

	if err != nil {
		entry.Errorf("get player:%d goods info error, %s", playerId, err)
		return nil, err
	}

	goodsInfoList := make(map[int64]*modelStore.GoodsBuyInfo, len(goodsInfoMap))

	for _, goodsInfoStr := range goodsInfoMap {
		goodsInfo := modelStore.NewGoodsBuyInfoJson(goodsInfoStr)
		if goodsInfo == nil {
			entry.Warnf("get player:%d goods info error, %v", playerId, goodsInfoStr)
			continue
		}

		goodsInfoList[goodsInfo.GoodsId] = goodsInfo
	}

	return goodsInfoList, nil
}

// UpdatePlayerGoodsBuyInfo 修改玩家购买商品信息
func UpdatePlayerGoodsBuyInfo(ctx context.Context, playerId uint64, goodsInfo *modelStore.GoodsBuyInfo) error {
	entry := logx.NewLogEntry(ctx)
	if playerId == 0 || goodsInfo == nil || !goodsInfo.IsValid() {
		entry.Warnf("update player:%d goods info error, goods info is nil or invalid", playerId)
		return fmt.Errorf("update player:%d goods info error, goods info is nil or invalid", playerId)
	}

	// 使用TxPipeline 设置后加上过期时间
	pipLine := redisx.GetPlayerCli().TxPipeline()
	pipLine.HSet(ctx, fmtPlayerGoodsBuyRdsKey(playerId), transform.Int642Str(goodsInfo.GoodsId), goodsInfo.ToJsonStr())
	pipLine.Expire(ctx, fmtPlayerGoodsBuyRdsKey(playerId), config.PLAYER_GOODS_BUY_EXPIRE)

	_, err := pipLine.Exec(ctx)
	if err != nil {
		entry.Errorf("update player:%d goods info:%v error, %s", playerId, *goodsInfo, err)
	}

	return err
}

// BatchUpdatePlayerGoodsBuyInfo 批量更新玩家商品购买信息
func BatchUpdatePlayerGoodsBuyInfo(ctx context.Context, playerId uint64, goodsInfoList []*modelStore.GoodsBuyInfo) error {
	entry := logx.NewLogEntry(ctx)
	if playerId == 0 || len(goodsInfoList) == 0 {
		entry.Warnf("batch update player:%d goods info error, goods info list is nil or invalid", playerId)
		return fmt.Errorf("batch update player:%d goods info error, goods info list is nil or invalid", playerId)
	}

	values := make([]string, len(goodsInfoList)*2)

	// 批量转换为字符串列表
	for _, goodsInfo := range goodsInfoList {
		if !goodsInfo.IsValid() {
			entry.Warnf("batch update player:%d goods info error, %v", playerId, *goodsInfo)
			continue
		}

		values = append(values, transform.Int642Str(goodsInfo.GoodsId))
		values = append(values, goodsInfo.ToJsonStr())
	}

	// 使用TxPipeline 设置后加上过期时间
	pipLine := redisx.GetPlayerCli().TxPipeline()
	pipLine.HSet(ctx, fmtPlayerGoodsBuyRdsKey(playerId), values)
	pipLine.Expire(ctx, fmtPlayerGoodsBuyRdsKey(playerId), config.PLAYER_GOODS_BUY_EXPIRE)

	_, err := pipLine.Exec(ctx)
	if err != nil {
		entry.Errorf("batch update player:%d goods info:%v error, %s", playerId, goodsInfoList, err)
	}

	return err
}
