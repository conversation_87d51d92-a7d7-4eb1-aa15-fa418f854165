package dao_ticket

import (
	"context"
	"errors"
	"hallsrv/internal/config"
	"hallsrv/internal/model/model_ticket"
	"time"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
	"git.keepfancy.xyz/back-end/frameworks/lib/timex"
	"git.keepfancy.xyz/back-end/frameworks/lib/transform"
	"github.com/go-redis/redis/v8"
)

// GetPondTicketInfo 查询玩家在某钓场门票信息，若无票返回 EntryAt=0, DurationSec=0
func GetPondTicketInfo(ctx context.Context, playerId uint64, pondId int64) (*model_ticket.PondTicketInfo, error) {
	entry := logx.NewLogEntry(ctx)
	key := config.GetRdsKeyPondTicket(playerId, pondId)
	cli := redisx.GetGameCli()

	// 使用HGETALL获取Hash中的所有字段
	result, err := cli.HGetAll(ctx, key).Result()
	if errors.Is(err, redis.Nil) {
		return &model_ticket.PondTicketInfo{}, nil
	}
	if err != nil {
		entry.Errorf("get pond ticket error: player=%d pond=%d err=%+v", playerId, pondId, err)
		return nil, err
	}

	info := &model_ticket.PondTicketInfo{}
	if err = transform.Map2Struct(result, info); err != nil {
		return nil, err
	}

	return info, nil
}

// BatchGetPondTicketInfo 批量查询门票信息
func BatchGetPondTicketInfo(ctx context.Context, playerId uint64, pondIds []int64) (map[int64]*model_ticket.PondTicketInfo, error) {
	cli := redisx.GetGameCli()
	pipe := cli.Pipeline()
	hGetAllCmds := make([]*redis.StringStringMapCmd, 0, len(pondIds))

	for _, pondId := range pondIds {
		key := config.GetRdsKeyPondTicket(playerId, pondId)
		hGetAllCmds = append(hGetAllCmds, pipe.HGetAll(ctx, key))
	}

	_, err := pipe.Exec(ctx)
	if err != nil && !errors.Is(err, redis.Nil) {
		return nil, err
	}

	ret := make(map[int64]*model_ticket.PondTicketInfo, len(pondIds))
	for i, pondId := range pondIds {
		info := &model_ticket.PondTicketInfo{}
		result := hGetAllCmds[i].Val()

		if err = transform.Map2Struct(result, info); err != nil {
			return nil, err
		}
		ret[pondId] = info
	}
	return ret, nil
}

// AddOrSetPondTicketTTLSeconds 使用门票叠加时长；返回叠加后的剩余秒数与首用时间
// 规则：若 key 不存在则设置为新增时长并写入 entry_at=now；存在则在现有 duration 基础上累加，上限封顶
func AddOrSetPondTicketTTLSeconds(ctx context.Context, playerId uint64, pondId int64, addSeconds int64) (newDuration int64, entryAt int64, err error) {
	entry := logx.NewLogEntry(ctx)

	key := config.GetRdsKeyPondTicket(playerId, pondId)
	cli := redisx.GetGameCli()

	// 获取当前门票信息
	currentInfo, err := GetPondTicketInfo(ctx, playerId, pondId)
	if err != nil {
		return 0, 0, err
	}

	// 计算新的时长
	newSec := currentInfo.DurationSec + addSeconds
	limitTime := int64(1000 * time.Hour.Seconds())
	if newSec > limitTime {
		newSec = limitTime
	}

	// 确定 entry_at（首次使用时间）
	entryAt = timex.Now().Unix()
	if currentInfo.EntryAt > 0 {
		entryAt = currentInfo.EntryAt // 保持首次使用时间不变
	}

	data := model_ticket.PondTicketInfo{
		PondId:      pondId,
		EntryAt:     entryAt,
		DurationSec: newSec,
	}

	fields := make(map[string]string)
	if err = transform.Struct2Map(data, fields); err != nil {
		return 0, 0, err
	}

	// 使用管道批量执行HMSet和Expire操作
	pipe := cli.Pipeline()
	pipe.HMSet(ctx, key, fields)
	pipe.Expire(ctx, key, time.Duration(newSec)*time.Second)

	_, err = pipe.Exec(ctx)
	if err != nil {
		entry.Errorf("set pond ticket hash and expire error: player=%d pond=%d err=%+v", playerId, pondId, err)
		return 0, 0, err
	}

	return newSec, entryAt, nil
}
