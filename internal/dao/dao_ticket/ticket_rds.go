package dao_ticket

import (
	"context"
	"errors"
	"fmt"
	"hallsrv/internal/config"
	"hallsrv/internal/model/model_ticket"
	"time"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
	"git.keepfancy.xyz/back-end/frameworks/lib/transform"
	"github.com/go-redis/redis/v8"
)

// GetPondTicketInfo 查询玩家在某钓场门票信息，若无票返回 EntryAt=0, DurationSec=0
func GetPondTicketInfo(ctx context.Context, playerId uint64, pondId int64) (*model_ticket.PondTicketInfo, error) {
	entry := logx.NewLogEntry(ctx)
	key := config.GetRdsKeyPondTicket(playerId, pondId)
	cli := redisx.GetGameCli()

	// 使用HGETALL获取Hash中的所有字段
	result, err := cli.HGetAll(ctx, key).Result()
	if errors.Is(err, redis.Nil) {
		return &model_ticket.PondTicketInfo{}, nil
	}
	if err != nil {
		entry.Errorf("get pond ticket error: player=%d pond=%d err=%+v", playerId, pondId, err)
		return nil, err
	}

	info := &model_ticket.PondTicketInfo{}
	if err = transform.Map2Struct(result, info); err != nil {
		return nil, err
	}

	return info, nil
}

// BatchGetPondTicketInfo 批量查询门票信息
func BatchGetPondTicketInfo(ctx context.Context, playerId uint64, pondIds []int64) (map[int64]*model_ticket.PondTicketInfo, error) {
	cli := redisx.GetGameCli()
	pipe := cli.Pipeline()
	hGetAllCmds := make([]*redis.StringStringMapCmd, 0, len(pondIds))

	for _, pondId := range pondIds {
		key := config.GetRdsKeyPondTicket(playerId, pondId)
		hGetAllCmds = append(hGetAllCmds, pipe.HGetAll(ctx, key))
	}

	_, err := pipe.Exec(ctx)
	if err != nil && !errors.Is(err, redis.Nil) {
		return nil, err
	}

	ret := make(map[int64]*model_ticket.PondTicketInfo, len(pondIds))
	for i, pondId := range pondIds {
		info := &model_ticket.PondTicketInfo{}
		result := hGetAllCmds[i].Val()

		if err = transform.Map2Struct(result, info); err != nil {
			return nil, err
		}
		ret[pondId] = info
	}
	return ret, nil
}

// SetPondTicketInfo 设置门票信息
// 参数：playerId 玩家ID；ticketInfo 门票信息结构体
// 返回：更新后的门票信息和错误
func SetPondTicketInfo(ctx context.Context, playerId uint64, ticketInfo *model_ticket.PondTicketInfo) (*model_ticket.PondTicketInfo, error) {
	entry := logx.NewLogEntry(ctx)

	if ticketInfo == nil {
		return nil, fmt.Errorf("ticketInfo is nil")
	}

	key := config.GetRdsKeyPondTicket(playerId, ticketInfo.PondId)
	cli := redisx.GetGameCli()

	fields := make(map[string]string)
	if err := transform.Struct2Map(ticketInfo, fields); err != nil {
		return nil, err
	}

	// 使用管道批量执行HMSet和Expire操作
	pipe := cli.Pipeline()
	pipe.HMSet(ctx, key, fields)
	pipe.Expire(ctx, key, time.Duration(ticketInfo.DurationSec)*time.Second)

	_, err := pipe.Exec(ctx)
	if err != nil {
		entry.Errorf("set pond ticket hash and expire error: player=%d pond=%d err=%+v", playerId, ticketInfo.PondId, err)
		return nil, err
	}

	return ticketInfo, nil
}
