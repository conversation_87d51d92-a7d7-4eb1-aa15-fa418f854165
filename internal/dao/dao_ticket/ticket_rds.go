package dao_ticket

import (
	"context"
	"errors"
	"hallsrv/internal/config"
	"strconv"
	"time"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
	"github.com/go-redis/redis/v8"
)

type PondTicketRdsInfo struct {
	EntryAt     int64
	DurationSec int64
}

// GetPondTicketInfo 查询玩家在某钓场门票信息，若无票返回 EntryAt=0, DurationSec=0
func GetPondTicketInfo(ctx context.Context, playerId uint64, pondId int64) (*PondTicketRdsInfo, error) {
	entry := logx.NewLogEntry(ctx)
	key := config.GetRdsKeyPondTicket(playerId, pondId)
	cli := redisx.GetGameCli()

	pipe := cli.Pipeline()
	ttlCmd := pipe.TTL(ctx, key)
	getCmd := pipe.Get(ctx, key)
	_, err := pipe.Exec(ctx)
	if err != nil && err != redis.Nil {
		entry.Errorf("get pond ticket error: player=%d pond=%d err=%+v", playerId, pondId, err)
		return nil, err
	}

	d := ttlCmd.Val()
	var durationSec int64 = 0
	if d > 0 {
		durationSec = int64(d.Seconds())
	}
	var entryAt int64 = 0
	if val, err2 := getCmd.Result(); err2 == nil {
		if ts, convErr := strconv.ParseInt(val, 10, 64); convErr == nil {
			entryAt = ts
		}
	}

	return &PondTicketRdsInfo{EntryAt: entryAt, DurationSec: durationSec}, nil
}

// BatchGetPondTicketInfo 批量查询门票信息
func BatchGetPondTicketInfo(ctx context.Context, playerId uint64, pondIds []int64) (map[int64]*PondTicketRdsInfo, error) {
	cli := redisx.GetGameCli()
	pipe := cli.Pipeline()
	getCmds := make([]*redis.StringCmd, 0, len(pondIds))

	for _, pondId := range pondIds {
		key := config.GetRdsKeyPondTicket(playerId, pondId)
		getCmds = append(getCmds, pipe.Get(ctx, key))
	}

	_, err := pipe.Exec(ctx)
	if err != nil && !errors.Is(err, redis.Nil) {
		return nil, err
	}

	ret := make(map[int64]*PondTicketRdsInfo, len(pondIds))
	for i, pondId := range pondIds {
		info := &PondTicketRdsInfo{}
		if val, err2 := getCmds[i].Result(); err2 == nil {

			if ts, convErr := strconv.ParseInt(val, 10, 64); convErr == nil {
				info.EntryAt = ts
			}
		}
		ret[pondId] = info
	}
	return ret, nil
}

// AddOrSetPondTicketTTLSeconds 使用门票叠加时长；返回叠加后的剩余秒数与首用时间
// 规则：以 TTL 作为剩余时长，若 key 不存在则设置为新增时长并写入 entry_at=now；存在则在现有 TTL 基础上累加，上限封顶
func AddOrSetPondTicketTTLSeconds(ctx context.Context, playerId uint64, pondId int64, addSeconds int64) (newDuration int64, entryAt int64, err error) {
	entry := logx.NewLogEntry(ctx)
	if addSeconds <= 0 {
		info, e := GetPondTicketInfo(ctx, playerId, pondId)
		if e != nil {
			return 0, 0, e
		}
		return info.DurationSec, info.EntryAt, nil
	}
	key := config.GetRdsKeyPondTicket(playerId, pondId)
	cli := redisx.GetGeneralCli()

	curTTL, e := cli.TTL(ctx, key).Result()
	if e != nil && e != redis.Nil {
		return 0, 0, e
	}
	curSec := int64(0)
	if curTTL > 0 {
		curSec = int64(curTTL.Seconds())
	}
	newSec := curSec + addSeconds
	if newSec > int64(config.TICKET_MAX_SECONDS) {
		newSec = int64(config.TICKET_MAX_SECONDS)
	}

	// 确定 entry_at（首次使用时间）
	entryAt = time.Now().Unix()
	if curSec > 0 {
		if val, err2 := cli.Get(ctx, key).Result(); err2 == nil {
			if ts, convErr := strconv.ParseInt(val, 10, 64); convErr == nil {
				entryAt = ts
			}
		}
	}

	// 写入 entry_at 作为 value，并设置过期 newSec
	_, err = cli.SetEX(ctx, key, strconv.FormatInt(entryAt, 10), time.Duration(newSec)*time.Second).Result()
	if err != nil {
		entry.Errorf("set pond ticket ttl error: player=%d pond=%d err=%+v", playerId, pondId, err)
		return 0, 0, err
	}
	return newSec, entryAt, nil
}
