package test

import (
	"context"
	"hallsrv/internal/repo/record"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_mysql"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_redis"
	"git.keepfancy.xyz/back-end/frameworks/dict"
	"git.keepfancy.xyz/back-end/frameworks/kit/nsqx"
	"git.keepfancy.xyz/back-end/frameworks/kit/rpc/interceptor"
	"git.keepfancy.xyz/back-end/frameworks/lib/logdog"
	"github.com/spf13/viper"
)

func Init() {
	logdog.SetupLog("debug", false)

	serverHost := "************"
	// serverHost := "localhost"
	viper.SetDefault(dict.ConfigConsulAddr, serverHost+":8500")
	viper.SetDefault(dict.ConfigKafkaUrl, serverHost+":9092")
	viper.SetDefault(dict.ConfigNsqDAddr, serverHost+":4150")

	// redis
	viper.SetDefault(dict.ConfigRedisAddr, serverHost+":6379")
	viper.SetDefault(dict.ConfigRedisPwd, "8888")

	rdsConf := map[string]string{
		"addr":   serverHost + ":6379",
		"passwd": "8888",
	}

	redisMap := map[string]interface{}{
		dict_redis.RDBGame:    rdsConf,
		dict_redis.RDBPlayer:  rdsConf,
		dict_redis.RDBGeneral: rdsConf,
		dict_redis.RDBLock:    rdsConf,
	}

	viper.SetDefault(dict.ConfigRedisList, redisMap)

	// nsq
	viper.SetDefault(dict.ConfigNsqDAddr, serverHost+":4150")
	viper.SetDefault(dict.ConfigNsqHttpAddr, serverHost+":4151")
	viper.SetDefault(dict.ConfigNsqLookUpdAddress, serverHost+":4161")
	nsqx.Setup()

	// sql
	InitSql()

	// 初始化流水
	record.DefaultLogging = record.NewHallRecordWorkPool()
	record.DefaultLogging.StartItemRecordWorkerPool()
}

func NewCtx() context.Context {
	ctx := interceptor.NewRpcClientCtx(
		interceptor.WithProductId(1),
		interceptor.WithCountry("zh-cn"),
	)
	return ctx
}
func NewCtxWithPlayerId(playerId uint64) context.Context {
	ctx := interceptor.NewRpcClientCtx(
		interceptor.WithProductId(1),
		interceptor.WithChannelType(1001),
		interceptor.WithCountry("zh-cn"),
		interceptor.WithPlayerId(playerId),
	)
	return ctx
}

func InitRedisConsul() {
	viper.SetDefault("consul_addr", "************:8500")
	viper.SetDefault("kafka_url", "************:9092")
	viper.SetDefault("nsqd_addr", "************:4150")

	conf := map[string]string{
		"addr":   "************:6379",
		"passwd": "8888",
	}
	viper.SetDefault("redis_list", map[string]interface{}{
		dict_redis.RDBGame:    conf,
		dict_redis.RDBPlayer:  conf,
		dict_redis.RDBGeneral: conf,
		dict_redis.RDBLock:    conf,
	})
}

func InitSql() {
	db := dict_mysql.MysqlDBGeneral
	conf := map[string]interface{}{
		"addr": "************:3306",
		// "addr":   "localhost:3306",
		"passwd": "fancydb2024#",
		"user":   "root",
		"db":     db,
	}

	viper.SetDefault(dict.ConfigMysqlList, map[string]interface{}{
		db: conf,
	})
}
