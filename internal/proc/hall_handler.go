package proc

import (
	"context"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	hallPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/hall"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/frameworks/kit/transport"
	intranetGrpc "git.keepfancy.xyz/back-end/frameworks/kit/transport/gaterpc"
)

// FisheryHandler 钓场相关
type HallHandler struct {
}

// 获取大厅实例
func GetHallInstance() *HallHandler {
	return &HallHandler{}
}

// FirstEnterHallReq 首次进入大厅请求
func (h *HallHandler) FirstEnterHallReq(ctx context.Context, _ *intranetGrpc.Header, req *hallPB.FirstEnterHallReq) *transport.ResponseMsg {
	rsp := GetHallServiceInstance().FirstEnterHallReq(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_FIRST_ENTER_HALL_RSP, rsp)
}

// GetRoomInfoReq 获取房间信息请求
func (h *HallHandler) GetRoomInfoReq(ctx context.Context, _ *intranetGrpc.Header, req *hallPB.GetRoomInfoReq) *transport.ResponseMsg {
	rsp := GetHallServiceInstance().GetPlayerRoomReq(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_GET_ROOM_INFO_RSP, rsp)
}

// GetLastGameInfo 获取玩家上次游戏信息
func (h *HallHandler) GetLastGameInfo(ctx context.Context, _ *intranetGrpc.Header, req *hallPB.GetLastGameInfoReq) *transport.ResponseMsg {
	rsp := GetHallServiceInstance().GetLastGameInfoReq(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_GET_LAST_GAME_INFO_RSP, rsp)
}

// EnterFisheryReq 进入钓场请求
func (h *HallHandler) EnterFisheryReq(ctx context.Context, _ *intranetGrpc.Header, req *hallPB.EnterFisheryReq) *transport.ResponseMsg {
	rsp := GetHallServiceInstance().EnterFisheryReq(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_ENTER_FISHERY_RSP, rsp)
}

// GetItemInfoReq 查询道具信息请求
func (h *HallHandler) GetItemInfoReq(ctx context.Context, _ *intranetGrpc.Header, req *hallPB.GetItemInfoReq) *transport.ResponseMsg {
	rsp := GetHallServiceInstance().GetItemInfoReq(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_GET_ITEM_INFO_RSP, rsp)
}

// GetItemInfoByTypeReq 根据类型查询道具信息
func (h *HallHandler) GetItemInfoByTypeReq(ctx context.Context, _ *intranetGrpc.Header, req *hallPB.GetItemInfoByTypeReq) *transport.ResponseMsg {
	rsp := GetHallServiceInstance().GetItemInfoByTypeReq(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_GET_ITEM_INFO_BY_TYPE_RSP, rsp)
}

// GetGoodsBuyInfoReq 商品购买信息请求
func (h *HallHandler) GetGoodsBuyInfoReq(ctx context.Context, _ *intranetGrpc.Header, req *hallPB.GetGoodsBuyInfoReq) *transport.ResponseMsg {
	rsp := GetHallServiceInstance().GetGoodsBuyInfoReq(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_GET_GOODS_BUY_INFO_RSP, rsp)
}

// StoreBuyReq 商城购买请求
func (h *HallHandler) StoreBuyReq(ctx context.Context, _ *intranetGrpc.Header, req *hallPB.StoreBuyReq) *transport.ResponseMsg {
	rsp := GetHallServiceInstance().StoreBuyReq(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_STORE_BUY_RSP, rsp)
}

// GetPlayerInfoReq 查询玩家信息
func (h *HallHandler) GetPlayerInfoReq(ctx context.Context, _ *intranetGrpc.Header, req *hallPB.GetPlayerInfoReq) *transport.ResponseMsg {
	rsp := GetHallServiceInstance().GetPlayerInfoReq(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_GET_PLAYER_INFO_RSP, rsp)
}

// UseItemReq 使用道具
func (h *HallHandler) UseItemReq(ctx context.Context, _ *intranetGrpc.Header, req *hallPB.UseItemReq) *transport.ResponseMsg {
	rsp := GetHallServiceInstance().UseItemReq(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_USE_ITEM_RSP, rsp)
}

// ItemCdReq 玩家道具冷却
func (h *HallHandler) ItemCdReq(ctx context.Context, _ *intranetGrpc.Header, req *hallPB.GetItemCdReq) *transport.ResponseMsg {
	rsp := GetHallServiceInstance().ItemCdReq(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_HALL_GET_ITEM_CD_RSP, rsp)
}

// StoreMultiBuyReq 商城批量购买
func (h *HallHandler) StoreMultiBuyReq(ctx context.Context, _ *intranetGrpc.Header, req *hallPB.StoreMultiBuyReq) *transport.ResponseMsg {
	rsp := GetHallServiceInstance().StoreMultiBuyReq(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_STORE_MULTI_BUY_RSP, rsp)
}

// GetStatList 获取统计列表
func (h *HallHandler) GetStatList(ctx context.Context, _ *intranetGrpc.Header, req *hallPB.GetStatListReq) *transport.ResponseMsg {
	rsp := GetHallServiceInstance().GetStatListReq(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_GET_STAT_LIST_RSP, rsp)
}

// GetStatRule 获取统计规则
func (h *HallHandler) GetStatsRule(ctx context.Context, _ *intranetGrpc.Header, req *hallPB.GetStatsRulesReq) *transport.ResponseMsg {
	rsp := GetHallServiceInstance().GetStatsRulesReq(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_HALL_GET_STATS_RULES_RSP, rsp)
}

// GetRodRigInfoReq 获取钓组规则信息
func (h *HallHandler) GetRodRigInfoReq(ctx context.Context, _ *intranetGrpc.Header, req *hallPB.GetRodRigInfoReq) *transport.ResponseMsg {
	rsp := GetHallServiceInstance().GetRodRigInfoReq(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_GET_ROD_RIG_INFO_RSP, rsp)
}

// UpdateRodRigInfoReq 更新钓组信息请求
func (h *HallHandler) UpdateRodRigInfoReq(ctx context.Context, _ *intranetGrpc.Header, req *hallPB.UpdateRodRigInfoReq) *transport.ResponseMsg {
	rsp := GetHallServiceInstance().UpdateRodRigInfoReq(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_UPDATE_ROD_RIG_INFO_RSP, rsp)
}

// DeleteRodRigInfoReq 删除钓组信息
func (h *HallHandler) DeleteRodRigInfoReq(ctx context.Context, _ *intranetGrpc.Header, req *hallPB.DeleteRodRigInfoReq) *transport.ResponseMsg {
	rsp := GetHallServiceInstance().DeleteRodRigInfoReq(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_DELETE_ROD_RIG_INFO_RSP, rsp)
}

// CheckForbidWordReq 屏蔽字请求
func (h *HallHandler) CheckForbidWordReq(ctx context.Context, _ *intranetGrpc.Header, req *hallPB.CheckForbidWordReq) *transport.ResponseMsg {
	rsp := GetHallServiceInstance().CheckForbidWordReq(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_CHECK_FORBID_WORD_RSP, rsp)
}

// ------------------------------------
// 背包系统
// ------------------------------------

// GetTripBagReq 获取旅行背包信息
func (h *HallHandler) GetTripBagReq(ctx context.Context, _ *intranetGrpc.Header, req *hallPB.GetTripBagReq) *transport.ResponseMsg {
	rsp := GetHallServiceInstance().GetTripBagReq(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_GET_TRIP_BAG_RSP, rsp)
}

// ModifyTripBagReq 修改旅行背包
func (h *HallHandler) ModifyTripBagReq(ctx context.Context, _ *intranetGrpc.Header, req *hallPB.ModifyTripBagReq) *transport.ResponseMsg {
	rsp := GetHallServiceInstance().ModifyTripBagReq(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_MODIFY_TRIP_BAG_RSP, rsp)
}

// UnloadAllTripBagReq 卸下旅行背包所有物品
func (h *HallHandler) UnloadAllTripBagReq(ctx context.Context, _ *intranetGrpc.Header, req *hallPB.UnloadAllTripBagReq) *transport.ResponseMsg {
	rsp := GetHallServiceInstance().UnloadAllTripBagReq(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_UNLOAD_TRIP_BAG_RSP, rsp)
}

// GetTripRodReq 获取杆包钓组
func (h *HallHandler) GetTripRodReq(ctx context.Context, _ *intranetGrpc.Header, req *hallPB.GetTripRodReq) *transport.ResponseMsg {
	rsp := GetHallServiceInstance().GetTripRodReq(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_GET_TRIP_ROD_RSP, rsp)
}

// LoadTripRodReq 加载预设钓组
func (h *HallHandler) LoadTripRodReq(ctx context.Context, _ *intranetGrpc.Header, req *hallPB.LoadTripRodReq) *transport.ResponseMsg {
	rsp := GetHallServiceInstance().LoadTripRodReq(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_LOAD_TRIP_ROD_RSP, rsp)
}

// DelTripRodReq 卸下鱼杆包
func (h *HallHandler) DelTripRodReq(ctx context.Context, _ *intranetGrpc.Header, req *hallPB.DelTripRodReq) *transport.ResponseMsg {
	rsp := GetHallServiceInstance().DelTripRodReq(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_DEL_TRIP_ROD_RSP, rsp)
}

// UpdateTripRodReq 更新杆包钓组
func (h *HallHandler) UpdateTripRodReq(ctx context.Context, _ *intranetGrpc.Header, req *hallPB.UpdateTripRodReq) *transport.ResponseMsg {
	rsp := GetHallServiceInstance().UpdateTripRodReq(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_UPDATE_TRIP_ROD_RSP, rsp)
}

// BatchUpdateTripRodReq 批量更新钓组
func (h *HallHandler) BatchUpdateTripRodReq(ctx context.Context, _ *intranetGrpc.Header, req *hallPB.BatchUpdateTripRodReq) *transport.ResponseMsg {
	rsp := GetHallServiceInstance().BatchUpdateTripRodReq(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_BATCH_UPDATE_TRIP_ROD_RSP, rsp)
}

// SaveTripRodReq 保存预设钓组
func (h *HallHandler) SaveTripRodReq(ctx context.Context, _ *intranetGrpc.Header, req *hallPB.SaveTripRodReq) *transport.ResponseMsg {
	rsp := GetHallServiceInstance().SaveTripRodReq(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_SAVE_TRIP_ROD_RSP, rsp)
}

// SaveNewTripRodReq 保存新钓组
func (h *HallHandler) SaveNewTripRodReq(ctx context.Context, _ *intranetGrpc.Header, req *hallPB.SaveNewTripRodReq) *transport.ResponseMsg {
	rsp := GetHallServiceInstance().SaveNewTripRodReq(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_SAVE_NEW_TRIP_ROD_RSP, rsp)
}

// PutTripRodReq 杆组放入背包
func (h *HallHandler) PutTripRodReq(ctx context.Context, _ *intranetGrpc.Header, req *hallPB.PutTripRodReq) *transport.ResponseMsg {
	rsp := GetHallServiceInstance().PutTripRodReq(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_PUT_TRIP_ROD_RSP, rsp)
}

// SplitTripRodReq 拆卸杆组
func (h *HallHandler) SplitTripRodReq(ctx context.Context, _ *intranetGrpc.Header, req *hallPB.SplitTripRodReq) *transport.ResponseMsg {
	rsp := GetHallServiceInstance().SplitTripRodReq(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_SPLIT_TRIP_ROD_RSP, rsp)
}

// ModifyDurabilityReq 修改鱼竿耐久度
func (h *HallHandler) ModifyDurabilityReq(ctx context.Context, _ *intranetGrpc.Header, req *hallPB.ModifyDurabilityReq) *transport.ResponseMsg {
	rsp := GetHallServiceInstance().ModifyDurabilityReq(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_MODIFY_DURABILITY_RSP, rsp)
}

// MaintainStorageItemReq 维修仓库道具
func (h *HallHandler) MaintainStorageItemReq(ctx context.Context, _ *intranetGrpc.Header, req *hallPB.MaintainStorageItemReq) *transport.ResponseMsg {
	rsp := GetHallServiceInstance().MaintainStorageItemReq(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_MAINTAIN_STORAGE_ITEM_RSP, rsp)
}

// MaintainRodItemReq 维修钓组道具
func (h *HallHandler) MaintainRodItemReq(ctx context.Context, _ *intranetGrpc.Header, req *hallPB.MaintainRodItemReq) *transport.ResponseMsg {
	rsp := GetHallServiceInstance().MaintainRodItemReq(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_MAINTAIN_ROD_ITEM_RSP, rsp)
}

// SellItemReq 卖掉道具
func (h *HallHandler) SellItemReq(ctx context.Context, _ *intranetGrpc.Header, req *hallPB.SellItemReq) *transport.ResponseMsg {
	rsp := GetHallServiceInstance().SellItemReq(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_SELL_ITEM_RSP, rsp)
}

// ItemHeapReq 耐久损耗度查询
func (h *HallHandler) ItemHeapReq(ctx context.Context, _ *intranetGrpc.Header, req *hallPB.ItemHeapReq) *transport.ResponseMsg {
	rsp := GetHallServiceInstance().ItemHeapReq(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_ITEM_HEAP_RSP, rsp)
}

// TempCashBuyReq 临时现金购买
func (h *HallHandler) TempCashBuyReq(ctx context.Context, _ *intranetGrpc.Header, req *hallPB.TempCashBuyReq) *transport.ResponseMsg {
	rsp := GetHallServiceInstance().TempCashBuyReq(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_TEMP_CASH_BUY_RSP, rsp)
}

// 新手引导进度更新请求
func (h *HallHandler) UpdateGuideProgressReq(ctx context.Context, _ *intranetGrpc.Header, req *hallPB.UpdateGuideProgressReq) *transport.ResponseMsg {
	rsp := GetHallServiceInstance().UpdateGuideProgressReq(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_HALL_UPDATE_GUIDE_PROGRESS_RSP, rsp)
}

// ContinuousLogin 连续登录活动查询
func (h *HallHandler) ContinuousLogin(ctx context.Context, _ *intranetGrpc.Header, req *hallPB.ContinuousLoginReq) *transport.ResponseMsg {
	rsp := GetHallServiceInstance().ContinuousLogin(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_HALL_CONTINUOUS_LOGIN_RSP, rsp)
}

// ContinuousLoginReward 连续登录活动领奖
func (h *HallHandler) ContinuousLoginReward(ctx context.Context, _ *intranetGrpc.Header, req *hallPB.ContinuousLoginRewardReq) *transport.ResponseMsg {
	rsp := GetHallServiceInstance().ContinuousLoginReward(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_HALL_CONTINUOUS_LOGIN_REWARD_RSP, rsp)
}

func (h *HallHandler) GetPlayerAllRedDot(ctx context.Context, _ *intranetGrpc.Header, req *hallPB.GetPlayerAllRedDotsRequest) *transport.ResponseMsg {
	rsp := GetHallServiceInstance().GetPlayerAllRedDot(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_GET_PLAYER_ALL_RED_DOT_RSP, rsp)
}

func (h *HallHandler) GetPlayerModuleRedDot(ctx context.Context, _ *intranetGrpc.Header, req *hallPB.GetPlayerModuleRedDotsRequest) *transport.ResponseMsg {
	rsp := GetHallServiceInstance().GetPlayerModuleRedDot(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_GET_PLAYER_MODULE_RED_DOT_RSP, rsp)
}

func (h *HallHandler) ClearPlayerModuleRedDot(ctx context.Context, _ *intranetGrpc.Header, req *hallPB.ClearRedDotRequest) *transport.ResponseMsg {
	rsp := GetHallServiceInstance().ClearPlayerModuleRedDot(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_CLEAR_RED_DOT_RSP, rsp)
}

func (h *HallHandler) ModifyPlayerInfoReq(ctx context.Context, _ *intranetGrpc.Header, req *hallPB.ModifyPlayerInfoReq) *transport.ResponseMsg {
	rsp := GetHallServiceInstance().ModifyPlayerInfoReq(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_HALL_MODIFY_PLAYER_INFO_RSP, rsp)
}

func (h *HallHandler) CDKeyExchange(ctx context.Context, _ *intranetGrpc.Header, req *hallPB.CDKeyExchangeReq) *transport.ResponseMsg {
	rsp := GetHallServiceInstance().CDKeyExchange(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_CDKEY_EXCHANGE_RSP, rsp)
}

func (h *HallHandler) PondStoreReq(ctx context.Context, _ *intranetGrpc.Header, req *hallPB.PondStoreReq) *transport.ResponseMsg {
	rsp := GetHallServiceInstance().PondStoreReq(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_HALL_POND_STORE_RSP, rsp)
}

func (h *HallHandler) PondStoreBuyReq(ctx context.Context, _ *intranetGrpc.Header, req *hallPB.PondStoreBuyReq) *transport.ResponseMsg {
	rsp := GetHallServiceInstance().PondStoreBuyReq(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_HALL_POND_STORE_BUY_RSP, rsp)
}

// GetCurPondTicketReq 获取钓场门票
func (h *HallHandler) GetCurPondTicketReq(ctx context.Context, _ *intranetGrpc.Header, req *hallPB.GetCurPondTicketReq) *transport.ResponseMsg {
	rsp := GetHallServiceInstance().GetCurPondTicketReq(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_HALL_GET_CUR_POND_TICKET_RSP, rsp)
}

// UseTicketReq 使用门票
func (h *HallHandler) UseTicketReq(ctx context.Context, _ *intranetGrpc.Header, req *hallPB.UseTicketReq) *transport.ResponseMsg {
	rsp := GetHallServiceInstance().UseTicketReq(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_HALL_USE_TICKET_RSP, rsp)
}
