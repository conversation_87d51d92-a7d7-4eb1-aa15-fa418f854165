// Package proc 一些游戏周边支持服务
package proc

import (
	"context"
	hallPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/hall"
	"hallsrv/internal/logic/logic_support"
	"sync"
)

type HallSupportService struct {
}

var (
	onceSupport     = &sync.Once{}
	instanceSupport *HallSupportService
)

func GetHallSupportServiceInstance() *HallSupportService {
	if instanceSupport != nil {
		return instanceSupport
	}

	onceSupport.Do(func() {
		instanceSupport = &HallSupportService{}
	})
	return instanceSupport
}

// RealNameVerifyReq 实名认证服务请求
func (h *HallSupportService) RealNameVerifyReq(ctx context.Context, req *hallPB.RealNameAuthReq) *hallPB.RealNameAuthRsp {
	return logic_support.VerifyRealName(ctx, req)
}
