package proc

import (
	"context"
	"fmt"
	logicRig "hallsrv/internal/logic/logic_rig"
	"hallsrv/internal/model/model_bag"
	"hallsrv/internal/repo/record"
	test_init "hallsrv/internal/test"
	"reflect"
	"testing"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/config"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	hallPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/hall"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/testx"
	"github.com/sirupsen/logrus"
)

func init() {
	test_init.Init()
	testx.InitKafka("************")
	record.Init()
	record.DefaultLogging = record.NewHallRecordWorkPool()
	record.DefaultLogging.StartItemRecordWorkerPool()
}

func TestHallService_GetTripRodReq(t *testing.T) {
	test_init.InitRedisConsul()
	test_init.InitSql()
	type args struct {
		ctx context.Context
		req *hallPB.GetTripRodReq
	}
	tests := []struct {
		name string
		args args
	}{
		// TODO: Add test cases.
		{
			args: args{
				ctx: test_init.NewCtx(),
				req: &hallPB.GetTripRodReq{Storage: commonPB.STORAGE_TYPE_ST_UNKNOWN},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &HallService{}
			got := s.GetTripRodReq(tt.args.ctx, tt.args.req)
			t.Error("=======================")
			t.Error("=======================")
			t.Error("=======================")
			t.Errorf("%+v", *got)
		})
	}
}

func TestHallService_BatchUpdateTripRodReq(t *testing.T) {
	test_init.InitRedisConsul()
	test_init.InitSql()

	type args struct {
		ctx context.Context
		req *hallPB.BatchUpdateTripRodReq
	}
	tests := []struct {
		name    string
		args    args
		wantRsp *hallPB.BatchUpdateTripRodRsp
	}{
		// TODO: Add test cases.
		{
			args: args{
				ctx: test_init.NewCtx(),
				req: &hallPB.BatchUpdateTripRodReq{
					Id:   1,
					Name: "第一大杆组",
					List: []*hallPB.UpdateTripRodReq{
						{
							Id:         1,
							Sit:        8,
							ItemId:     0,
							InstanceId: "",
							Storage:    1,
						},
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &HallService{}
			if gotRsp := s.BatchUpdateTripRodReq(tt.args.ctx, tt.args.req); !reflect.DeepEqual(gotRsp, tt.wantRsp) {
				t.Errorf("BatchUpdateTripRodReq() = %v, want %v", gotRsp, tt.wantRsp)
			}
		})
	}
}

func TestGetTripRodReq(t *testing.T) {
	test_init.Init()
	s := &HallService{}
	ctx := test_init.NewCtxWithPlayerId(12)
	req := &hallPB.GetTripRodReq{Storage: commonPB.STORAGE_TYPE_ST_BAG}
	rsp := s.GetTripRodReq(ctx, req)
	if rsp.Ret.Code != commonPB.ErrCode_ERR_SUCCESS {
		t.Errorf("err:%+v", rsp)
	}
	fmt.Printf("rsp:%+v", rsp)
}

func TestModifyDurability(t *testing.T) {
	test_init.Init()
	s := &HallService{}
	ctx := test_init.NewCtxWithPlayerId(900066)
	req := &hallPB.ModifyDurabilityReq{
		RigId: 1,
	}
	rsp := s.ModifyDurabilityReq(ctx, req)
	t.Errorf("err:%+v", rsp)
}

func TestContinuousLoginReward(t *testing.T) {
	test_init.Init()
	s := &HallService{}
	ctx := test_init.NewCtxWithPlayerId(900069)
	req := &hallPB.ContinuousLoginRewardReq{}
	rsp := s.ContinuousLoginReward(ctx, req)
	fmt.Printf("ContinuousLoginReward response: %+v", rsp)
}

func TestSaveNewRod(t *testing.T) {
	testx.Init()
	s := &HallService{}
	playerId := uint64(88)
	channelId := int32(1001)
	ctx := testx.TestCtx(playerId, channelId)
	req := &hallPB.SaveNewTripRodReq{
		Name: "第一大杆组",
		List: []*hallPB.UpdateTripRodReq{
			{
				Sit:    int32(commonPB.TRIP_ROD_SIT_TRS_BAIT),
				ItemId: 30920200101,
				// InstanceId: "ddd4b7ff-57e2-11f0-b187-080027d1b278",
				// Storage:    commonPB.STORAGE_TYPE_ST_STORE,
			},
		},
	}
	rsp := s.SaveNewTripRodReq(ctx, req)
	fmt.Printf("SaveNewTripRodReq response: %+v", rsp)
}

// 单个更新
func TestUpdateRod(t *testing.T) {
	testx.Init()
	s := &HallService{}
	playerId := uint64(88)
	channelId := int32(1001)
	ctx := testx.TestCtx(playerId, channelId)
	req := &hallPB.UpdateTripRodReq{
		Id:  350,
		Sit: int32(commonPB.TRIP_ROD_SIT_TRS_BAIT),
		// ItemId: 30810200101,
	}
	rsp := s.UpdateTripRodReq(ctx, req)
	fmt.Printf("UpdateTripRodReq response: %+v", rsp)
}

// 批量更新
func TestBatchUpdateRod(t *testing.T) {
	testx.Init()
	s := &HallService{}
	playerId := uint64(88)
	channelId := int32(1001)
	ctx := testx.TestCtx(playerId, channelId)
	req := &hallPB.BatchUpdateTripRodReq{
		Id: 350,
		List: []*hallPB.UpdateTripRodReq{
			{
				Sit:    5,
				ItemId: 30920200101,
			},
		},
	}
	rsp := s.BatchUpdateTripRodReq(ctx, req)
	fmt.Printf("BatchUpdateTripRodReq response: %+v", rsp)
}

func TestLossDurability(t *testing.T) {
	testx.Init()
	s := &HallService{}
	playerId := uint64(88)
	channelId := int32(1001)
	ctx := testx.TestCtx(playerId, channelId)
	req := &hallPB.ModifyDurabilityReq{
		RigId: 349,
		Change: map[int32]int64{
			5: 100,
		},
	}
	rsp := s.ModifyDurabilityReq(ctx, req)
	fmt.Printf("ModifyDurabilityReq response: %+v", rsp)
}

func TestSpliteRod(t *testing.T) {
	testx.Init()
	s := &HallService{}
	playerId := uint64(88)
	channelId := int32(1001)
	ctx := testx.TestCtx(playerId, channelId)
	req := &hallPB.SplitTripRodReq{
		Id: 349,
	}
	rsp := s.SplitTripRodReq(ctx, req)
	fmt.Printf("DelTripRodReq response: %+v", rsp)
}

func TestCheckRule(t *testing.T) {
	testx.Init()
	// s := &HallService{}
	playerId := uint64(88)
	channelId := int32(1001)
	logicRig.LoadRigRuleCache()
	serviceConfig := config.NewServiceConfig()
	serviceConfig.Register("InitRigRuleCfg", cmodel.InitRigRuleCfg) // 钓组规则配置

	ctx := testx.TestCtx(playerId, channelId)
	// 构造trip rod group结构
	tripRodGroup := &model_bag.TTripRodGroup{
		Id:       342,
		PlayerId: playerId,
		Name:     "基础浮漂竿F350-MH",
		BagIndex: 2,
		Data: map[int32]*model_bag.TTripRod{
			1: {
				PlayerId:   playerId,
				Id:         342,
				SitId:      1,
				ItemId:     301500206,
				InstanceId: "5583df9f-57e4-11f0-a6af-080027d1b278",
				Args: map[int32]int64{
					1: 216000,
					2: 216000,
				},
			},
			2: {
				PlayerId:   playerId,
				Id:         342,
				SitId:      2,
				ItemId:     302100101,
				InstanceId: "5583e17c-57e4-11f0-a6af-080027d1b278",
				Args: map[int32]int64{
					1: 432000,
					2: 432000,
				},
			},
			3: {
				PlayerId:   playerId,
				Id:         342,
				SitId:      3,
				ItemId:     303100101,
				InstanceId: "5583e2fc-57e4-11f0-a6af-080027d1b278",
				Args: map[int32]int64{
					1: 216000,
					2: 216000,
				},
			},
			5: {
				PlayerId:   playerId,
				Id:         342,
				SitId:      5,
				ItemId:     30511100201,
				InstanceId: "5583e49d-57e4-11f0-a6af-080027d1b278",
			},
			6: {
				PlayerId:   playerId,
				Id:         342,
				SitId:      6,
				ItemId:     306500101,
				InstanceId: "5583e49d-57e4-11f0-a6af-080027d1b278",
				Args: map[int32]int64{
					1: 576000,
					2: 576000,
				},
			},
			7: {
				PlayerId:   playerId,
				Id:         342,
				SitId:      7,
				ItemId:     30810200201,
				InstanceId: "5583e49d-57e4-11f0-a6af-080027d1b278",
			},
		},
	}
	tpyList := tripRodGroup.GetSubItemList(ctx)
	rule := logicRig.GetRigRuleId(tpyList)
	if rule == 0 {
		t.Fatalf("rule is 0")
	}
	ruleStr := logicRig.SubTypeArrToStr(tpyList)
	t.Errorf("get ruleStr:%s", ruleStr)
}

func TestPondStore(t *testing.T) {
	testx.Init()
	s := &HallService{}
	playerId := uint64(88)
	channelId := int32(1001)
	ctx := testx.TestCtx(playerId, channelId)
	req := &hallPB.PondStoreReq{
		PondId: []int64{301020000},
	}
	rsp := s.PondStoreReq(ctx, req)
	logrus.Infof("GetPondStoreReq response: %+v", rsp)
}

func TestPondStoreBuy(t *testing.T) {
	testx.Init()
	s := &HallService{}
	playerId := uint64(88)
	channelId := int32(1001)
	ctx := testx.TestCtx(playerId, channelId)
	req := &hallPB.PondStoreBuyReq{
		PondId: 301020000,
		Batch:  1,
		GoodId: 10301400104,
		Count:  1,
	}
	rsp := s.PondStoreBuyReq(ctx, req)
	fmt.Printf("PondStoreBuyReq response: %+v", rsp)
}
