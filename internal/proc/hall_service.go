package proc

import (
	"context"
	daoPond "hallsrv/internal/dao/dao_pond"
	logicAnn "hallsrv/internal/logic/logic_ann"
	"hallsrv/internal/logic/logic_bag"
	logicBag "hallsrv/internal/logic/logic_bag"
	logicCdk "hallsrv/internal/logic/logic_cdk"
	"hallsrv/internal/logic/logic_continuos_login"
	"hallsrv/internal/logic/logic_durable"
	logicGuide "hallsrv/internal/logic/logic_guide"
	logicHall "hallsrv/internal/logic/logic_hall"
	logicItem "hallsrv/internal/logic/logic_item"
	logicPlayer "hallsrv/internal/logic/logic_player"
	logicPond "hallsrv/internal/logic/logic_pond"
	"hallsrv/internal/logic/logic_pond_store"
	logic "hallsrv/internal/logic/logic_red_dot"
	logicRig "hallsrv/internal/logic/logic_rig"
	"hallsrv/internal/logic/logic_stats"
	logicStore "hallsrv/internal/logic/logic_store"
	logic_ticket "hallsrv/internal/logic/logic_ticket"
	modelPond "hallsrv/internal/model/model_pond"
	modelReddot "hallsrv/internal/model/model_red_dot"
	modelRodRig "hallsrv/internal/model/model_rod_rig"
	rpcTrip "hallsrv/internal/repo/rpc_trip"
	"sync"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	hallPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/hall"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/rpc_biz/crpc_stats"
	"git.keepfancy.xyz/back-end/frameworks/kit/forbid"
	"git.keepfancy.xyz/back-end/frameworks/kit/rpc/interceptor"
)

type HallService struct {
}

var (
	once     = &sync.Once{}
	instance *HallService
)

func GetHallServiceInstance() *HallService {
	if instance != nil {
		return instance
	}

	once.Do(func() {
		instance = &HallService{}
	})
	return instance
}

// FirstEnterHallReq 首次进入大厅请求
func (h *HallService) FirstEnterHallReq(ctx context.Context, req *hallPB.FirstEnterHallReq) *hallPB.FirstEnterHallRsp {
	entry := logx.NewLogEntry(ctx)
	rsp := &hallPB.FirstEnterHallRsp{}

	opts := interceptor.GetRPCOptions(ctx)
	playerId := opts.PlayerId
	channelId := opts.ChannelType
	entry.Debugf("FirstEnterHallReq req playerId=%d channelId=%d", playerId, channelId)

	playerInfo, err := logicPlayer.QueryPlayerInfo(ctx, playerId)
	if err != nil {
		entry.Errorf("player:%d, query player info error: %v", playerId, err)
	}

	popInfo, err := logicAnn.QueryPopupInfo(ctx, channelId)
	if err != nil {
		entry.Errorf("query popup info error: %v", err)
	}

	logicHall.PlayerFirstEnterHall(ctx, playerId, req.GetIsReg())

	rsp.PlayerInfo = playerInfo
	rsp.PopupInfo = popInfo

	entry.Debugf("player:%d, first enter hall success, req:%s, rsp:%s", playerId, req.String(), rsp.String())

	return rsp
}

// GetPlayerRoomReq 查询玩家所在房间请求
func (h *HallService) GetPlayerRoomReq(ctx context.Context, req *hallPB.GetRoomInfoReq) *hallPB.GetRoomInfoRsp {
	entry := logx.NewLogEntry(ctx)
	rsp := &hallPB.GetRoomInfoRsp{Ret: protox.FillCodeResult(commonPB.ErrCode_ERR_FAIL)}

	playerId := interceptor.GetRPCOptions(ctx).PlayerId

	roomInfo, err := rpcTrip.RpcGetPlayerRoomInfo(ctx, playerId)
	if err != nil || roomInfo == nil {
		rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_FAIL)
		rsp.Ret.Desc = "not in fishery in room"
		entry.Debugf("get player:%d, query room info:%+v ret:%v", playerId, roomInfo, err)
		return rsp
	}

	// 取钓点ID
	lastGameInfo, err := daoPond.QueryPlayerLastGameInfo(ctx, playerId)
	if lastGameInfo == nil || err != nil {
		rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_FAIL)
		entry.Warnf("player:%d, query last game info error: %v", playerId, err)
		return rsp
	}

	rsp.RoomInfo = roomInfo
	rsp.RoomInfo.SpotId = lastGameInfo.SpotId
	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)

	entry.Debugf("player:%d, query room info success, req:%s, rsp:%s", playerId, req.String(), rsp.String())

	return rsp
}

// GetLastGameInfoReq 查询上次游戏信息请求
func (h *HallService) GetLastGameInfoReq(ctx context.Context, req *hallPB.GetLastGameInfoReq) *hallPB.GetLastGameInfoRsp {
	entry := logx.NewLogEntry(ctx)
	rsp := &hallPB.GetLastGameInfoRsp{Ret: protox.DefaultResult()}

	playerId := interceptor.GetRPCOptions(ctx).PlayerId
	lastGameInfo, err := daoPond.QueryPlayerLastGameInfo(ctx, playerId)
	if err != nil || lastGameInfo == nil {
		rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_FAIL, "query last game info error")
		entry.Errorf("get player:%d, query last game info error:%v", playerId, err)
		return rsp
	}

	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)
	rsp.LastGame = lastGameInfo.ToProto()

	entry.Debugf("player:%d, query last game info success, ctx:%v, req:%s, rsp:%s", playerId, ctx, req.String(), rsp.String())

	return rsp
}

// EnterFisheryReq 进入钓点请求
func (h *HallService) EnterFisheryReq(ctx context.Context, req *hallPB.EnterFisheryReq) *hallPB.EnterFisheryRsp {
	entry := logx.NewLogEntry(ctx)
	rsp := &hallPB.EnterFisheryRsp{Ret: protox.DefaultResult()}

	playerId := interceptor.GetRPCOptions(ctx).PlayerId

	roomInfo, errCode := logicPond.PlayerEntryPond(ctx, playerId, req.GetPondId(), req.GetSpotId(), req.GetGameType(), req.GetRoomType())
	if errCode != commonPB.ErrCode_ERR_SUCCESS && roomInfo == nil {
		rsp.Ret = protox.FillCodeResult(errCode, "enter fishery error")
		entry.Errorf("EnterFisheryReq, player:%d, pondId:%d, roomType:%d, enter fishery errCode:%d", playerId, req.GetPondId(), req.GetRoomType(), errCode)
		return rsp
	}

	rsp.RoomInfo = roomInfo
	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)

	// 记录玩家上次游戏信息
	lastGameInfo := modelPond.NewLastGameInfo(playerId, req.GetPondId(), int32(req.GetRoomType()), req.GetSpotId())
	daoPond.UpdatePlayerLastGameInfo(ctx, playerId, lastGameInfo)

	entry.Debugf("player:%d entry pond success, req:%s, rsp:%s", playerId, req.String(), rsp.String())

	return rsp
}

// GetItemInfoReq 获取道具信息请求
func (h *HallService) GetItemInfoReq(ctx context.Context, req *hallPB.GetItemInfoReq) *hallPB.GetItemInfoRsp {
	entry := logx.NewLogEntry(ctx)
	rsp := &hallPB.GetItemInfoRsp{Ret: protox.DefaultResult()}
	rsp.ItemList = req.GetItemList()

	playerId := interceptor.GetRPCOptions(ctx).PlayerId

	itemList, err := logicItem.PlayerQueryItemInfoByIdList(ctx, playerId, req.GetItemList())
	if err != nil {
		rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_FAIL, "query item info error")
		entry.Errorf("get player:%d, item list:%v, query item info error:%v", playerId, req.GetItemList(), err)
		return rsp
	}

	rsp.ItemInfo = itemList
	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)

	entry.Debugf("get player:%d, item list:%v, query item info, success, req:%s, rsp:%s", playerId, req.GetItemList(), req.String(), rsp.String())

	return rsp
}

// GetItemInfoByTypeReq 根据道具类型查询道具信息列表
func (h *HallService) GetItemInfoByTypeReq(ctx context.Context, req *hallPB.GetItemInfoByTypeReq) *hallPB.GetItemInfoByTypeRsp {
	entry := logx.NewLogEntry(ctx)
	rsp := &hallPB.GetItemInfoByTypeRsp{Ret: protox.DefaultResult()}

	playerId := interceptor.GetRPCOptions(ctx).PlayerId

	itemList, err := logicItem.PlayerQueryItemInfo(ctx, playerId, req.GetItemType())
	if err != nil {
		rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_FAIL, "query item info error")
		entry.Errorf("get player:%d, item type:%d, query item info error:%v", playerId, req.GetItemType(), err)
		return rsp
	}

	rsp.ItemInfo = itemList
	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)

	entry.Debugf("get player:%d, item type:%d, query item info success, req:%s, rsp:%s", playerId, req.GetItemType(), req.String(), rsp.String())

	return rsp
}

// GetGoodsBuyInfoReq 商品购买信息请求
func (h *HallService) GetGoodsBuyInfoReq(ctx context.Context, req *hallPB.GetGoodsBuyInfoReq) *hallPB.GetGoodsBuyInfoRsp {
	rsp := &hallPB.GetGoodsBuyInfoRsp{Ret: protox.DefaultResult()}
	entry := logx.NewLogEntry(ctx)

	playerId := interceptor.GetRPCOptions(ctx).PlayerId

	// 计算逻辑
	goodsBuyInfos, err := logicStore.GetGoodsBuyInfo(ctx, playerId, req.GetGoodsList())
	if err != nil {
		rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_FAIL, "query goods buy info error")
		entry.Errorf("get player:%d, goodsList:%v, buy info error:%v", playerId, req.GetGoodsList(), err)
		return rsp
	}

	rsp.BuyInfo = goodsBuyInfos
	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)

	entry.Infof("get player:%d buy multi goods info success, req:%s, rsp:%s", playerId, req.String(), rsp.String())

	return rsp
}

// StoreBuyReq 商城购买请求
func (h *HallService) StoreBuyReq(ctx context.Context, req *hallPB.StoreBuyReq) *hallPB.StoreBuyRsp {
	rsp := &hallPB.StoreBuyRsp{Ret: protox.DefaultResult()}
	entry := logx.NewLogEntry(ctx)

	playerId := interceptor.GetRPCOptions(ctx).PlayerId

	rsp.Count = req.GetCount()
	rsp.StoreBuyId = req.GetStoreBuyId()

	// 购买流程
	rewardInfo, err := logicStore.BuyStoreGoods(ctx, playerId, req.GetStyleType(), req.GetStoreBuyId(), req.GetCount())
	if err != nil || rewardInfo == nil {
		rsp.Ret = protox.FillErrResult(err)
		entry.Errorf("player:%d, buy store buy id:%d count:%d err:%+v", playerId, req.GetStoreBuyId(), req.GetCount(), err)
		return rsp
	}

	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)
	rsp.RewardInfo = rewardInfo

	entry.Infof("player:%d, buy store success, req:%s, rsp:%s", playerId, req.String(), rsp.String())

	return rsp
}

// GetPlayerInfoReq 玩家信息请求
func (h *HallService) GetPlayerInfoReq(ctx context.Context, req *hallPB.GetPlayerInfoReq) *hallPB.GetPlayerInfoRsp {
	rsp := &hallPB.GetPlayerInfoRsp{}
	entry := logx.NewLogEntry(ctx)

	playerId := interceptor.GetRPCOptions(ctx).PlayerId
	queryPlayerId := req.GetPlayerId()

	playerInfo, err := logicPlayer.QueryPlayerInfo(ctx, queryPlayerId)
	if err != nil {
		entry.Errorf("player:%d, query player info error: %v", queryPlayerId, err)
	}

	rsp.PlayerInfo = playerInfo

	entry.Debugf("player:%d, query player info req:%s rsp:%s", playerId, req.String(), rsp.String())

	return rsp
}

// UseItemReq 使用道具
func (h *HallService) UseItemReq(ctx context.Context, req *hallPB.UseItemReq) *hallPB.UseItemRsp {
	rsp := &hallPB.UseItemRsp{Ret: protox.DefaultResult()}
	playerId := interceptor.GetRPCOptions(ctx).PlayerId
	entry := logx.NewLogEntry(ctx)

	if len(req.GetItemInfo()) <= 0 {
		rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_BAD_PARAM, "item info is empty")
		entry.Errorf("player:%d, use item req param error: %s", playerId, rsp.String())
		return rsp
	}

	// 直接扣除道具 不成功就道具不足
	response, err := logicItem.OperatePlayerItem(ctx, playerId, req.GetItemInfo(), commonPB.ITEM_OPERATION_IO_REDUCE, req.GetSrcType(), req.GetStorage(), false)
	if err != nil {
		rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_HALL_ITEM_NOT_ENOUGH, err.Error())
		entry.Errorf("player:%d, use item req:%s error: %v", playerId, req.String(), err)
		return rsp
	}

	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)
	if response != nil {
		rsp.Timestamp = response.Timestamp
	}

	entry.Debugf("player:%d, use item req:%s success", playerId, req.String())

	return rsp
}

// StoreMultiBuyReq 商城批量购买(针对非限制商品)
func (h *HallService) StoreMultiBuyReq(ctx context.Context, req *hallPB.StoreMultiBuyReq) *hallPB.StoreMultiBuyRsp {
	rsp := &hallPB.StoreMultiBuyRsp{
		Ret:      protox.DefaultResult(),
		GoosList: req.GetGoodsList(),
	}

	entry := logx.NewLogEntry(ctx)

	playerId := interceptor.GetRPCOptions(ctx).PlayerId
	if len(req.GetGoodsList()) <= 0 {
		rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_BAD_PARAM, "param error")
		entry.Errorf("player:%d, store multi buy req param error: %v", playerId, rsp.String())
		return rsp
	}

	rewardInfo, err := logicStore.PlayerBuyMultiStoreGoods(ctx, playerId, req.GetStyleType(), req.GetGoodsList())
	if err != nil {
		rsp.Ret = protox.FillErrResult(err)
		entry.Errorf("player:%d, store multi buy req err:%+v", playerId, err)
		return rsp
	}

	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)
	rsp.RewardInfo = rewardInfo

	entry.Debugf("player:%d, store multi buy req success, req:%v, rsp:%v", playerId, req.String(), rsp.String())

	return rsp
}

// GetRodRigInfoReq 查询竿组信息请求
func (h *HallService) GetRodRigInfoReq(ctx context.Context, req *hallPB.GetRodRigInfoReq) *hallPB.GetRodRigInfoRsp {
	rsp := &hallPB.GetRodRigInfoRsp{Ret: protox.DefaultResult()}
	playerId := interceptor.GetRPCOptions(ctx).PlayerId

	rigArr, err := logicRig.QueryPlayerRodRigInfo(ctx, playerId)
	entry := logx.NewLogEntry(ctx)

	if err != nil {
		rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_FAIL, err.Error())
		entry.Errorf("player:%d, get rod rig info req:%s error: %v", playerId, req.String(), err)
		return rsp
	}

	for _, rigInfo := range rigArr {
		if rigInfo == nil {
			continue
		}

		if req.GetRigId() > 0 && rigInfo.RigId != req.GetRigId() {
			continue
		} else {
			rsp.RigList = append(rsp.RigList, rigInfo.ToProto())
		}
	}

	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)

	entry.Debugf("player:%d, get rod rig info req:%s success, rsp:%s", playerId, req.String(), rsp.String())

	return rsp
}

// UpdateRodRigInfoReq 更新竿组信息请求
func (h *HallService) UpdateRodRigInfoReq(ctx context.Context, req *hallPB.UpdateRodRigInfoReq) *hallPB.UpdateRodRigInfoRsp {
	rsp := &hallPB.UpdateRodRigInfoRsp{
		Ret:     protox.DefaultResult(),
		RigInfo: req.GetRigInfo(),
	}
	entry := logx.NewLogEntry(ctx)

	playerId := interceptor.GetRPCOptions(ctx).PlayerId

	if errCode := logicRig.UpdatePlayerRodRigInfo(ctx, playerId, modelRodRig.NewRodRigInfoFromProto(playerId, req.GetRigInfo())); errCode != commonPB.ErrCode_ERR_SUCCESS {
		rsp.Ret = protox.FillCodeResult(errCode)
		entry.Errorf("player:%d, get rod rig info req:%s error: %v", playerId, req.String(), errCode)
		return rsp
	}

	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)

	entry.Debugf("player:%d, update rod rig info req:%s success", playerId, req.String())

	return rsp
}

// DeleteRodRigInfoReq 删除竿组信息请求
func (h *HallService) DeleteRodRigInfoReq(ctx context.Context, req *hallPB.DeleteRodRigInfoReq) *hallPB.DeleteRodRigInfoRsp {
	rsp := &hallPB.DeleteRodRigInfoRsp{
		Ret:   protox.DefaultResult(),
		RigId: req.GetRigId(),
	}
	entry := logx.NewLogEntry(ctx)

	playerId := interceptor.GetRPCOptions(ctx).PlayerId

	if err := logicRig.DeletePlayerRodRigInfo(ctx, playerId, req.GetRigId()); err != nil {
		rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_FAIL, err.Error())
		entry.Errorf("player:%d, delete rod rig info req:%s error: %v", playerId, req.String(), err)
		return rsp
	}

	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)

	entry.Debugf("player:%d, delete rod rig info req:%s success", playerId, req.String())

	return rsp
}

// GetStatListReq 获取统计列表
func (s *HallService) GetStatListReq(ctx context.Context, req *hallPB.GetStatListReq) *hallPB.GetStatListRsp {
	rsp := &hallPB.GetStatListRsp{
		Ret: protox.DefaultResult(),
	}
	opts := interceptor.GetRPCOptions(ctx)

	// TODO 这里后续数据多了 需要加上类型区分
	rtn, err := crpc_stats.RpcGetStatsList(ctx, opts.ProductId, opts.PlayerId)
	if err != nil {
		rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SYSTEM_MISTAKE, err.Error())
		return rsp
	}

	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)
	rsp.List = rtn

	return rsp
}

// CheckForbidWordReq 检查屏蔽字
func (s *HallService) CheckForbidWordReq(ctx context.Context, req *hallPB.CheckForbidWordReq) *hallPB.CheckForbidWordRsp {
	entry := logx.NewLogEntry(ctx)
	checkWord := req.GetWord()
	existForbid := forbid.ExistForbid(checkWord)
	afterWord := checkWord
	if existForbid {
		afterWord = forbid.FilterForbid(checkWord)
	}

	playerId := interceptor.GetRPCOptions(ctx).PlayerId

	entry.Debugf("player:%d, check word:%s, after word:%s", playerId, checkWord, afterWord)

	return &hallPB.CheckForbidWordRsp{
		IsForbid:   existForbid,
		ForbidWord: afterWord,
	}
}

// GetTripBagReq 查询旅行背包
func (s *HallService) GetTripBagReq(ctx context.Context, req *hallPB.GetTripBagReq) *hallPB.GetTripBagRsp {
	entry := logx.NewLogEntry(ctx)
	entry.Debugf("GetTripBagReq req %+v", req)

	rsp := &hallPB.GetTripBagRsp{
		Ret: protox.DefaultResult(),
	}
	opts := interceptor.GetRPCOptions(ctx)
	_ = opts

	list, err := logicBag.GetTripBagItem(ctx, opts.PlayerId, req.GetType())
	if err != nil {
		rsp.Ret = protox.FillErrResult(err)
		return rsp
	}

	rsp.List = list
	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)
	rsp.Type = req.Type
	entry.Debugf("GetTripBagReq rsp %+v", rsp)
	return rsp
}

// ModifyTripBagReq 修改旅行背包
func (s *HallService) ModifyTripBagReq(ctx context.Context, req *hallPB.ModifyTripBagReq) *hallPB.ModifyTripBagRsp {
	entry := logx.NewLogEntry(ctx)
	entry.Debugf("ModifyTripBagReq req %+v", req)

	rsp := &hallPB.ModifyTripBagRsp{
		Ret: protox.DefaultResult(),
	}
	var err error

	opts := interceptor.GetRPCOptions(ctx)

	list := make([]*commonPB.ItemBase, 0)
	list = append(list, &commonPB.ItemBase{
		ItemId:     req.GetItemId(),
		ItemCount:  req.GetCount(),
		InstanceId: req.GetInstanceId(),
	})

	err = logicBag.ModifyTripBag(ctx, opts.PlayerId, req.GetType(), commonPB.ITEM_OPERATION(req.GetOperate()), list)
	if err != nil {
		entry.Debugf("modify trip bag err: %v", err)
		rsp.Ret = protox.FillErrResult(err)
		return rsp
	}

	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)
	rsp.Type = req.Type
	rsp.Operate = req.Operate
	rsp.ItemId = req.ItemId

	entry.Debugf("ModifyTripBagReq rsp %+v", rsp)

	return rsp
}

// UnloadAllTripBagReq 卸下旅行背包所有物品
func (s *HallService) UnloadAllTripBagReq(ctx context.Context, req *hallPB.UnloadAllTripBagReq) *hallPB.UnloadAllTripBagRsp {
	entry := logx.NewLogEntry(ctx)
	entry.Debugf("UnloadAllTripBagReq req %+v", req)

	rsp := &hallPB.UnloadAllTripBagRsp{
		Ret: protox.DefaultResult(),
	}
	opts := interceptor.GetRPCOptions(ctx)
	_ = opts

	err := logicBag.UnloadAllTripBag(ctx, opts.PlayerId)
	if err != nil {
		entry.Debugf("modify trip bag err: %v", err)
		rsp.Ret = protox.FillErrResult(err)
		return rsp
	}

	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)
	entry.Debugf("UnloadAllTripBagReq rsp %+v", rsp)

	return rsp
}

// GetTripRodReq 获取杆包钓组
func (s *HallService) GetTripRodReq(ctx context.Context, req *hallPB.GetTripRodReq) *hallPB.GetTripRodRsp {
	entry := logx.NewLogEntry(ctx)
	entry.Debugf("GetTripRodReq req %+v", req)

	rsp := &hallPB.GetTripRodRsp{
		Ret: protox.DefaultResult(),
	}
	opts := interceptor.GetRPCOptions(ctx)
	_ = opts
	var err error
	arr, err := logicBag.GetAllRodBag(ctx, opts.PlayerId)

	if err != nil {
		rsp.Ret = protox.FillErrResult(err)
		return rsp
	}
	rsp.List = []*commonPB.RodBagInfo{}
	for _, data := range arr {
		rsp.List = append(rsp.List, data.ToProto(ctx))
	}
	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)
	entry.Debugf("GetTripRodReq rsp %+v", rsp)

	return rsp
}

// LoadTripRodReq 加载预设钓组
func (s *HallService) LoadTripRodReq(ctx context.Context, req *hallPB.LoadTripRodReq) *hallPB.LoadTripRodRsp {
	rsp := &hallPB.LoadTripRodRsp{
		Ret: protox.DefaultResult(),
	}
	opts := interceptor.GetRPCOptions(ctx)
	entry := logx.NewLogEntry(ctx)
	var err error
	info, err := logicBag.LoadRodBag(ctx, opts.PlayerId, req.GetRigId(), req.GetId())
	if err != nil {
		entry.Debugf("load trip fail %+v", err)
		rsp.Ret = protox.FillErrResult(err)
		return rsp
	}
	rsp.Info = info
	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)
	return rsp
}

// SaveTripRodReq 保存预设钓组
func (s *HallService) SaveTripRodReq(ctx context.Context, req *hallPB.SaveTripRodReq) *hallPB.SaveTripRodRsp {
	entry := logx.NewLogEntry(ctx)
	entry.Debugf("SaveTripRodReq req %+v", req)

	rsp := &hallPB.SaveTripRodRsp{
		Ret: protox.DefaultResult(),
	}
	opts := interceptor.GetRPCOptions(ctx)

	var err error
	info, err := logicBag.SaveRodStore(ctx, opts.PlayerId, req.GetRigId())
	if err != nil {
		entry.Debugf("load trip fail %+v", err)
		rsp.Ret = protox.FillErrResult(err)
		return rsp
	}
	rsp.Info = info
	rsp.RigId = req.GetRigId()
	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)
	entry.Debugf("SaveTripRodReq rsp %+v", rsp)
	return rsp
}

// SaveNewTripRodReq 保存新钓组
func (s *HallService) SaveNewTripRodReq(ctx context.Context, req *hallPB.SaveNewTripRodReq) *hallPB.SaveNewTripRodRsp {
	entry := logx.NewLogEntry(ctx)
	entry.Debugf("SaveNewTripRodReq req %+v", req)

	rsp := &hallPB.SaveNewTripRodRsp{
		Ret: protox.DefaultResult(),
	}
	opts := interceptor.GetRPCOptions(ctx)

	var err error
	info, err := logicBag.SaveNewRodStore(ctx, opts.PlayerId, req)
	if err != nil {
		entry.Debugf("save new trip fail %+v", err)
		rsp.Ret = protox.FillErrResult(err)
		return rsp
	}
	// if req.GetBagIndex() != 0 {
	// 	info, err = logicBag.PutRodBag(ctx, opts.PlayerId, info.Id, req.GetBagIndex())
	// 	if err != nil {
	// 		entry.Debugf("put bag fail %+v", err)
	// 		rsp.Ret = protox.FillErrResult(err)
	// 		return rsp
	// 	}
	// }
	// 是否放入背包
	rsp.Info = info
	rsp.Id = info.Id
	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)
	entry.Debugf("SaveNewTripRodReq rsp %+v", rsp)
	return rsp
}

// PutTripRodReq 放入背包
func (s *HallService) PutTripRodReq(ctx context.Context, req *hallPB.PutTripRodReq) *hallPB.PutTripRodRsp {
	entry := logx.NewLogEntry(ctx)
	entry.Debugf("PutTripRodReq req %+v", req)

	rsp := &hallPB.PutTripRodRsp{
		Ret: protox.DefaultResult(),
	}
	opts := interceptor.GetRPCOptions(ctx)
	var err error
	info, err := logicBag.PutRodBag(ctx, opts.PlayerId, req.GetId(), req.GetBagIndex())
	if err != nil {
		entry.Debugf("load trip fail %+v", err)
		rsp.Ret = protox.FillErrResult(err)
		return rsp
	}
	rsp.Info = info
	rsp.Id = req.GetId()
	rsp.BagIndex = req.GetBagIndex()
	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)

	entry.Debugf("PutTripRodReq rsp %+v", rsp)
	return rsp
}

// SplitTripRodReq 拆卸杆组
func (s *HallService) SplitTripRodReq(ctx context.Context, req *hallPB.SplitTripRodReq) *hallPB.SplitTripRodRsp {
	entry := logx.NewLogEntry(ctx)
	entry.Debugf("SplitTripRodReq req %+v", req)

	rsp := &hallPB.SplitTripRodRsp{
		Ret: protox.DefaultResult(),
	}

	opts := interceptor.GetRPCOptions(ctx)
	err := logicBag.SplitRodBag(ctx, opts.PlayerId, req.GetId())
	if err != nil {
		entry.Debugf("split trip fail %+v", err)
		rsp.Ret = protox.FillErrResult(err)
		return rsp
	}

	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)
	rsp.Id = req.GetId()
	entry.Debugf("SplitTripRodReq rsp %+v", rsp)
	return rsp
}

// DelTripRodReq 卸下杆包钓组
func (s *HallService) DelTripRodReq(ctx context.Context, req *hallPB.DelTripRodReq) (rsp *hallPB.DelTripRodRsp) {
	entry := logx.NewLogEntry(ctx)
	entry.Debugf("DelTripRodReq req %+v", req)
	rsp = &hallPB.DelTripRodRsp{
		Ret: protox.DefaultResult(),
	}

	opts := interceptor.GetRPCOptions(ctx)
	info, err := logicBag.DelRodBag(ctx, opts.PlayerId, req.GetId())
	if err != nil {
		entry.Debugf("del trip rod fail %+v", err)
		rsp.Ret = protox.FillErrResult(err)
		return rsp
	}

	rsp.Info = info
	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)
	rsp.Id = req.GetId()
	entry.Debugf("DelTripRodReq rsp %+v", rsp)
	return rsp
}

// UpdateTripRodReq 更新杆包钓组
func (s *HallService) UpdateTripRodReq(ctx context.Context, req *hallPB.UpdateTripRodReq) (rsp *hallPB.UpdateTripRodRsp) {
	rsp = &hallPB.UpdateTripRodRsp{
		Ret: protox.DefaultResult(),
	}
	opts := interceptor.GetRPCOptions(ctx)
	var err error
	group, err := logicBag.UpdateRodBag(ctx, opts.PlayerId, req)
	if err != nil {
		rsp.Ret = protox.FillErrResult(err)
		return rsp
	}
	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)
	rsp.Id = req.GetId()
	rsp.InstanceId = req.GetInstanceId()
	rsp.Info = group.ToProto(ctx)
	return rsp
}

// BatchUpdateTripRodReq 批量更新杆包钓组
func (s *HallService) BatchUpdateTripRodReq(ctx context.Context, req *hallPB.BatchUpdateTripRodReq) (rsp *hallPB.BatchUpdateTripRodRsp) {
	entry := logx.NewLogEntry(ctx)
	entry.Debugf("BatchUpdateTripRodReq req %+v", req)

	rsp = &hallPB.BatchUpdateTripRodRsp{
		Ret: protox.DefaultResult(),
	}
	opts := interceptor.GetRPCOptions(ctx)
	var err error

	group, err := logicBag.BatchUpdateRodBag(ctx, opts.PlayerId, req)
	if err != nil {
		rsp.Ret = protox.FillErrResult(err)
		return rsp
	}
	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)
	rsp.Info = group.ToProto(ctx)
	entry.Debugf("BatchUpdateTripRodReq rsp %+v", rsp)

	return rsp
}

// ModifyDurabilityReq 修改鱼竿耐久度
func (s *HallService) ModifyDurabilityReq(ctx context.Context, req *hallPB.ModifyDurabilityReq) (rsp *hallPB.ModifyDurabilityRsp) {
	rsp = &hallPB.ModifyDurabilityRsp{}
	opts := interceptor.GetRPCOptions(ctx)
	err := logic_bag.LossRodDurability(ctx, opts.PlayerId, req.RigId, req.GetChange())
	if err != nil {
		rsp.Ret = protox.FillErrResult(err)
		return rsp
	}
	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)
	return rsp
}

// MaintainStorageItemReq 修复仓库道具
func (s *HallService) MaintainStorageItemReq(ctx context.Context, req *hallPB.MaintainStorageItemReq) (rsp *hallPB.MaintainStorageItemRsp) {
	entry := logx.NewLogEntry(ctx)
	entry.Debugf("MaintainStorageItemReq req %+v", req)

	rsp = &hallPB.MaintainStorageItemRsp{Ret: protox.DefaultResult()}
	opts := interceptor.GetRPCOptions(ctx)
	reward, err := logic_durable.FixStorageItem(ctx, opts.PlayerId, req.GetInstanceId(), req.GetStoreType())
	if err != nil {
		rsp.Ret = protox.FillErrResult(err)
		return rsp
	}
	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)
	rsp.Cost = reward
	entry.Debugf("MaintainStorageItemReq rsp %+v", rsp)
	return rsp
}

// MaintainRodItemReq 修复鱼竿耐久度
func (s *HallService) MaintainRodItemReq(ctx context.Context, req *hallPB.MaintainRodItemReq) (rsp *hallPB.MaintainRodItemRsp) {
	entry := logx.NewLogEntry(ctx)
	entry.Debugf("MaintainRodItemReq req %+v", req)
	rsp = &hallPB.MaintainRodItemRsp{Ret: protox.DefaultResult()}

	opts := interceptor.GetRPCOptions(ctx)
	reward, rodInfo, err := logic_bag.FixRodDurability(ctx, opts.PlayerId, req.GetRigId(), req.GetSitId())
	if err != nil {
		rsp.Ret = protox.FillErrResult(err)
		return rsp
	}
	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)
	rsp.Cost = reward
	rsp.RodInfo = rodInfo.ToProto(ctx)
	entry.Debugf("MaintainRodItemReq rsp %+v", rsp)

	return rsp
}

// SellItemReq 出售道具
func (s *HallService) SellItemReq(ctx context.Context, req *hallPB.SellItemReq) (rsp *hallPB.SellItemRsp) {
	entry := logx.NewLogEntry(ctx)
	entry.Debugf("sellItem req %+v", req)
	rsp = &hallPB.SellItemRsp{Ret: protox.DefaultResult()}
	opts := interceptor.GetRPCOptions(ctx)
	reward, err := logicStore.SellItem(ctx, opts.PlayerId, req.GetStoreType(), req.GetList())
	if err != nil {
		rsp.Ret = protox.FillErrResult(err)
		return rsp
	}
	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)
	rsp.Reward = reward
	return rsp
}

// ItemHeapReq 鱼饵请求
func (s *HallService) ItemHeapReq(ctx context.Context, req *hallPB.ItemHeapReq) (rsp *hallPB.ItemHeapRsp) {
	entry := logx.NewLogEntry(ctx)
	entry.Debugf("itemHeap req %+v", req)
	rsp = &hallPB.ItemHeapRsp{Ret: protox.DefaultResult()}
	opts := interceptor.GetRPCOptions(ctx)
	itemHeap, err := logic_durable.QueryPlayerAllItemHeapDurable(ctx, opts.PlayerId)
	if err != nil {
		rsp.Ret = protox.FillErrResult(err)
		return rsp
	}
	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)
	rsp.ItemHeaps = logic_durable.ItemHeapToProto(itemHeap)
	entry.Debugf("itemHeap rsp %+v", rsp)
	return rsp
}

// TempCashBuyReq 临时金币购买
func (s *HallService) TempCashBuyReq(ctx context.Context, req *hallPB.TempCashBuyReq) (rsp *hallPB.TempCashBuyRsp) {
	entry := logx.NewLogEntry(ctx)
	entry.Debugf("tempCashBuy req %+v", req)
	rsp = &hallPB.TempCashBuyRsp{Ret: protox.DefaultResult()}
	opts := interceptor.GetRPCOptions(ctx)
	reward, err := logicStore.TempCashBuy(ctx, opts.PlayerId, req.GetStoreBuyId())
	if err != nil {
		rsp.Ret = protox.FillErrResult(err)
		return rsp
	}
	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)
	rsp.RewardInfo = reward
	rsp.StoreBuyId = req.GetStoreBuyId()

	entry.Debugf("tempCashBuy rsp %+v", rsp)

	return rsp
}

// UpdateGuideProgressReq 新手引导进度更新请求
func (s *HallService) UpdateGuideProgressReq(ctx context.Context, req *hallPB.UpdateGuideProgressReq) (rsp *hallPB.UpdateGuideProgressRsp) {
	entry := logx.NewLogEntry(ctx)
	entry.Debugf("updateGuideProgress req %+v", req)

	opts := interceptor.GetRPCOptions(ctx)
	rsp = &hallPB.UpdateGuideProgressRsp{Ret: protox.DefaultResult(), Progress: req.GetProgress()}

	// 更新新手引导进度
	err := logicGuide.UpdateGuideProgress(ctx, opts.PlayerId, req.GetProgress())
	if err != nil {
		rsp.Ret = protox.FillErrResult(err)
		return rsp
	}

	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)

	return rsp
}

// ContinuousLogin 登录连续
func (s *HallService) ContinuousLogin(ctx context.Context, req *hallPB.ContinuousLoginReq) (rsp *hallPB.ContinuousLoginRsp) {
	rsp = &hallPB.ContinuousLoginRsp{Ret: protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)}
	entry := logx.NewLogEntry(ctx)
	entry.Debugf("ContinoutsLoginReq:%+v", req)
	opts := interceptor.GetRPCOptions(ctx)

	data, err := logic_continuos_login.GetContinuousLogin(ctx, opts.PlayerId)
	if err != nil {
		rsp.Ret = protox.FillErrResult(err)
		return rsp
	}
	rsp.Day = data.Day
	rsp.UpdateTs = data.UpdateAt
	entry.Debugf("ContinoutsLoginReq Successs %+v", rsp)
	return rsp
}

// ContinuousLoginReward 登录连续奖励
func (s *HallService) ContinuousLoginReward(ctx context.Context, req *hallPB.ContinuousLoginRewardReq) (rsp *hallPB.ContinuousLoginRewardRsp) {
	rsp = &hallPB.ContinuousLoginRewardRsp{Ret: protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)}

	entry := logx.NewLogEntry(ctx)
	entry.Debugf("ContinoutsLoginRewardReq:%+v", req)
	opts := interceptor.GetRPCOptions(ctx)

	data, reward, err := logic_continuos_login.RewardContinuosLogin(ctx, opts.PlayerId)
	if data != nil {
		rsp.Day = data.Day
		rsp.UpdateTs = data.UpdateAt
	}
	if err != nil {
		rsp.Ret = protox.FillErrResult(err)
		return rsp
	}

	rsp.Reward = reward

	entry.Debugf("ContinoutsLoginRewardReq Successs %+v", rsp)
	return rsp
}

func (s *HallService) GetPlayerAllRedDot(ctx context.Context, req *hallPB.GetPlayerAllRedDotsRequest) (rsp *hallPB.GetPlayerAllRedDotsResponse) {
	rsp = &hallPB.GetPlayerAllRedDotsResponse{Ret: protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)}

	entry := logx.NewLogEntry(ctx)
	entry.Debugf("GetPlayerAllRedDot:%+v", req)
	opts := interceptor.GetRPCOptions(ctx)

	dots, err := logic.GetPlayerAllRedDots(ctx, opts.PlayerId)
	if err != nil {
		rsp.Ret = protox.FillErrResult(err)
		return rsp
	}

	rsp.RedDots = dots
	entry.Debugf("GetPlayerAllRedDot success: playerID=%d, count=%d", opts.PlayerId, len(dots))

	return rsp
}

func (s *HallService) GetPlayerModuleRedDot(ctx context.Context, req *hallPB.GetPlayerModuleRedDotsRequest) (rsp *hallPB.GetPlayerModuleRedDotsResponse) {
	rsp = &hallPB.GetPlayerModuleRedDotsResponse{Ret: protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)}

	entry := logx.NewLogEntry(ctx)
	entry.Debugf("GetPlayerModuleRedDot:%+v", req)
	opts := interceptor.GetRPCOptions(ctx)

	// 获取指定模块的红点
	dots, err := logic.GetPlayerModuleRedDots(ctx, opts.PlayerId, req.GetModuleType())
	if err != nil {
		entry.Errorf("get player module red dots failed: playerID=%d, moduleType=%d, err=%v",
			opts.PlayerId, req.GetModuleType(), err)
		rsp.Ret = protox.FillErrResult(err)
		return rsp
	}

	rsp.RedDots = dots
	entry.Debugf("get player module red dots success: playerID=%d, moduleType=%d, count=%d",
		opts.PlayerId, req.GetModuleType(), len(dots))

	return rsp
}

func (s *HallService) ClearPlayerModuleRedDot(ctx context.Context, req *hallPB.ClearRedDotRequest) (rsp *hallPB.ClearRedDotResponse) {
	rsp = &hallPB.ClearRedDotResponse{Ret: protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)}

	entry := logx.NewLogEntry(ctx)
	entry.Debugf("ClearPlayerModuleRedDot:%+v", req)
	opts := interceptor.GetRPCOptions(ctx)

	// 清除指定红点
	dot, err := logic.ClearRedDot(ctx, opts.PlayerId, req.GetModuleType(), modelReddot.SubModuleType(req.GetSubModuleType()))
	if err != nil {
		entry.Errorf("clear player module red dot failed: playerID=%d, moduleType=%d, subModuleType=%d, err=%v",
			opts.PlayerId, req.GetModuleType(), req.GetSubModuleType(), err)
		rsp.Ret = protox.FillErrResult(err)
		return rsp
	}

	rsp.RedDot = dot
	entry.Debugf("clear player module red dot success: playerID=%d, moduleType=%d, subModuleType=%d",
		opts.PlayerId, req.GetModuleType(), req.GetSubModuleType())

	return rsp
}

// ModifyPlayerInfoReq 修改玩家信息请求
func (s *HallService) ModifyPlayerInfoReq(ctx context.Context, req *hallPB.ModifyPlayerInfoReq) (rsp *hallPB.ModifyPlayerInfoRsp) {
	rsp = &hallPB.ModifyPlayerInfoRsp{Ret: protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)}
	entry := logx.NewLogEntry(ctx)
	entry.Debugf("ModifyPlayerInfoReq:%+v", req)
	opts := interceptor.GetRPCOptions(ctx)

	// 修改玩家信息
	err := logicPlayer.ModifyPlayerInfo(ctx, opts.PlayerId, req)
	if err != nil {
		rsp.Ret = protox.FillErrResult(err)
		return rsp
	}

	entry.Debugf("ModifyPlayerInfoReq Successs %+v", rsp)
	return rsp
}

// CDKeyExchange 兑换cdk
func (s *HallService) CDKeyExchange(ctx context.Context, req *hallPB.CDKeyExchangeReq) (rsp *hallPB.CDKeyExchangeRsp) {
	rsp = &hallPB.CDKeyExchangeRsp{Ret: protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)}
	entry := logx.NewLogEntry(ctx)
	entry.Debugf("CDKeyExchangeReq:%+v", req)
	opts := interceptor.GetRPCOptions(ctx)

	// 兑换cdk
	reward, err := logicCdk.CDKeyExchange(ctx, opts.PlayerId, req)
	if err != nil {
		rsp.Ret = protox.FillErrResult(err)
		entry.Debugf("CDKeyExchangeReq Failed %+v", rsp)
		return rsp
	}
	rsp.RewardInfo = reward

	entry.Debugf("CDKeyExchangeReq Successs %+v", rsp)
	return rsp
}

// ItemCdReq 玩家道具cd
func (s *HallService) ItemCdReq(ctx context.Context, req *hallPB.GetItemCdReq) (rsp *hallPB.GetItemCdRsp) {
	rsp = &hallPB.GetItemCdRsp{Ret: protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)}
	entry := logx.NewLogEntry(ctx)
	entry.Debugf("ItemCdReq:%+v", req)
	opts := interceptor.GetRPCOptions(ctx)

	// 获取道具cd
	cdList, err := logicItem.GetPlayerItemCd(ctx, opts.PlayerId)
	if err != nil {
		rsp.Ret = protox.FillErrResult(err)
		entry.Debugf("ItemCdReq Failed %+v", rsp)
		return rsp
	}
	rsp.List = cdList

	entry.Debugf("ItemCdReq Successs %+v", rsp)
	return rsp
}

// GetStatsRules 获取统计规则
func (s *HallService) GetStatsRulesReq(ctx context.Context, req *hallPB.GetStatsRulesReq) (rsp *hallPB.GetStatsRulesRsp) {
	rsp = &hallPB.GetStatsRulesRsp{Ret: protox.DefaultResult()}
	entry := logx.NewLogEntry(ctx)
	entry.Debugf("GetStatsRulesReq:%+v", req)
	opts := interceptor.GetRPCOptions(ctx)

	rules, err := logic_stats.GetStatsRules(ctx, opts.PlayerId, req.GetList())
	if err != nil {
		entry.Warnf("get stats rules failed: playerID=%d, err=%v",
			opts.PlayerId, err)
		rsp.Ret = protox.FillErrResult(err)
		return rsp
	}

	rsp.List = rules
	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)
	entry.Debugf("GetStatsRulesReq Successs %+v", rsp)
	return rsp
}

// PondStoreReq 局内商城请求
func (s *HallService) PondStoreReq(ctx context.Context, req *hallPB.PondStoreReq) (rsp *hallPB.PondStoreRsp) {
	rsp = &hallPB.PondStoreRsp{Ret: protox.DefaultResult()}
	entry := logx.NewLogEntry(ctx)
	entry.Debugf("PondStoreReq:%+v", req)
	opts := interceptor.GetRPCOptions(ctx)
	_ = opts

	pondStore, err := logic_pond_store.QueryPondStore(ctx, opts.PlayerId, req.GetPondId())
	if err != nil {
		rsp.Ret = protox.FillErrResult(err)
		entry.Debugf("PondStoreReq Failed %+v", rsp)
		return rsp
	}

	rsp.List = pondStore
	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)

	entry.Debugf("PondStoreReq Successs %+v", rsp)
	return rsp
}

// PondStoreBuyReq 局内商城购买请求
func (s *HallService) PondStoreBuyReq(ctx context.Context, req *hallPB.PondStoreBuyReq) (rsp *hallPB.PondStoreBuyRsp) {
	rsp = &hallPB.PondStoreBuyRsp{Ret: protox.DefaultResult()}
	entry := logx.NewLogEntry(ctx)
	entry.Debugf("PondStoreBuyReq:%+v", req)
	opts := interceptor.GetRPCOptions(ctx)
	buyVo, err := logic_pond_store.BuyPondStoreGoods(ctx, opts.PlayerId, req.GetPondId(), req.GetBatch(), req.GetGoodId(), req.GetCount())
	if err != nil {
		rsp.Ret = protox.FillErrResult(err)
		entry.Debugf("PondStoreBuyReq Failed %+v", rsp)
		return rsp
	}
	rsp.PondId = req.GetPondId()
	rsp.Batch = req.GetBatch()
	rsp.Good = buyVo.PondGood.ToProto()
	rsp.Reward = buyVo.Reward
	rsp.Good.Bought = buyVo.Bought

	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)
	entry.Debugf("PondStoreBuyReq Successs %+v", rsp)

	return rsp
}

// GetCurPondTicketReq 获取钓场门票信息
func (s *HallService) GetCurPondTicketReq(ctx context.Context, req *hallPB.GetCurPondTicketReq) (rsp *hallPB.GetCurPondTicketRsp) {
	rsp = &hallPB.GetCurPondTicketRsp{Ret: protox.DefaultResult()}
	entry := logx.NewLogEntry(ctx)
	opts := interceptor.GetRPCOptions(ctx)

	tickets, err := logic_ticket.GetCurPondTicket(ctx, opts.PlayerId, req.GetPondId())
	if err != nil {
		rsp.Ret = protox.FillErrResult(err)
		entry.Errorf("GetCurPondTicketReq error: player=%d err=%+v", opts.PlayerId, err)
		return rsp
	}
	rsp.TicketInfos = tickets
	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)
	return rsp
}

// UseTicketReq 使用门票叠加时长
func (s *HallService) UseTicketReq(ctx context.Context, req *hallPB.UseTicketReq) (rsp *hallPB.UseTicketRsp) {
	rsp = &hallPB.UseTicketRsp{Ret: protox.DefaultResult()}
	entry := logx.NewLogEntry(ctx)
	opts := interceptor.GetRPCOptions(ctx)

	info, err := logic_ticket.UseTicketWithItemReduce(ctx, opts.PlayerId, req.GetItemId(), req.GetCount(), req.GetPondId())
	if err != nil {
		rsp.Ret = protox.FillErrResult(err)
		entry.Errorf("UseTicket error: player=%d req=%s err=%+v", opts.PlayerId, req.String(), err)
		return rsp
	}

	rsp.TicketInfo = info
	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)
	return rsp
}
