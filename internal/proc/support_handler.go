package proc

import (
	"context"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	hallPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/hall"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/frameworks/kit/transport"
	intranetGrpc "git.keepfancy.xyz/back-end/frameworks/kit/transport/gaterpc"
)

type SupportHandler struct {
}

func GetSupportInstance() *SupportHandler {
	return &SupportHandler{}
}

// RealNameAuthReq 实名认证请求
func (s *SupportHandler) RealNameAuthReq(ctx context.Context, _ *intranetGrpc.Header, req *hallPB.RealNameAuthReq) *transport.ResponseMsg {
	rsp := GetHallSupportServiceInstance().RealNameVerifyReq(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_REAL_NAME_AUTH_RSP, rsp)
}
