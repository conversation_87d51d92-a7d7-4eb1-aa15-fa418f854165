package proc

import (
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/frameworks/kit/transport"
)

// RegClientMsgHandler 注册客户端消息Handler
func RegClientMsgHandler() {
	regHallHandler()
	RegSupportHandler()
}

func regHallHandler() {
	handler := GetHallInstance()

	// 首次进入大厅请求
	transport.Handler(int(commonPB.MsgID_CMD_FIRST_ENTER_HALL_REQ), handler.FirstEnterHallReq)
	// 获取房间信息请求
	transport.Handler(int(commonPB.MsgID_CMD_GET_ROOM_INFO_REQ), handler.GetRoomInfoReq)
	// 获取上次游戏信息请求
	transport.Handler(int(commonPB.MsgID_CMD_GET_LAST_GAME_INFO_REQ), handler.GetLastGameInfo)
	// 进入钓场请求
	transport.Handler(int(commonPB.MsgID_CMD_ENTER_FISHERY_REQ), handler.EnterFisheryReq)
	// 道具信息请求
	transport.Handler(int(commonPB.MsgID_CMD_GET_ITEM_INFO_REQ), handler.GetItemInfoReq)
	// 根据类型查询道具信息请求
	transport.Handler(int(commonPB.MsgID_CMD_GET_ITEM_INFO_BY_TYPE_REQ), handler.GetItemInfoByTypeReq)
	// 道具信息请求
	transport.Handler(int(commonPB.MsgID_CMD_GET_GOODS_BUY_INFO_REQ), handler.GetGoodsBuyInfoReq)
	// 商城购买请求
	transport.Handler(int(commonPB.MsgID_CMD_STORE_BUY_REQ), handler.StoreBuyReq)
	// 查询玩家信息
	transport.Handler(int(commonPB.MsgID_CMD_GET_PLAYER_INFO_REQ), handler.GetPlayerInfoReq)
	// 玩家使用道具
	transport.Handler(int(commonPB.MsgID_CMD_USE_ITEM_REQ), handler.UseItemReq)
	// 玩家道具冷却
	transport.Handler(int(commonPB.MsgID_CMD_HALL_GET_ITEM_CD_REQ), handler.ItemCdReq)
	// 商城批量购买(不同道具)
	transport.Handler(int(commonPB.MsgID_CMD_STORE_MULTI_BUY_REQ), handler.StoreMultiBuyReq)
	// 查询钓组信息
	transport.Handler(int(commonPB.MsgID_CMD_GET_ROD_RIG_INFO_REQ), handler.GetRodRigInfoReq)
	// 更新钓组信息
	transport.Handler(int(commonPB.MsgID_CMD_UPDATE_ROD_RIG_INFO_REQ), handler.UpdateRodRigInfoReq)
	// 删除钓组信息
	transport.Handler(int(commonPB.MsgID_CMD_DELETE_ROD_RIG_INFO_REQ), handler.DeleteRodRigInfoReq)
	// 获取玩家统计信息
	transport.Handler(int(commonPB.MsgID_CMD_GET_STAT_LIST_REQ), handler.GetStatList)
	// 获取统计规则
	transport.Handler(int(commonPB.MsgID_CMD_HALL_GET_STATS_RULES_REQ), handler.GetStatsRule)

	// 屏蔽字检测请求
	transport.Handler(int(commonPB.MsgID_CMD_CHECK_FORBID_WORD_REQ), handler.CheckForbidWordReq)

	// 获取旅行背包
	transport.Handler(int(commonPB.MsgID_CMD_GET_TRIP_BAG_REQ), handler.GetTripBagReq)
	// 修改旅行背包
	transport.Handler(int(commonPB.MsgID_CMD_MODIFY_TRIP_BAG_REQ), handler.ModifyTripBagReq)
	// 卸下旅行背包所有物品
	transport.Handler(int(commonPB.MsgID_CMD_UNLOAD_TRIP_BAG_REQ), handler.UnloadAllTripBagReq)

	// 获取杆包钓组
	transport.Handler(int(commonPB.MsgID_CMD_GET_TRIP_ROD_REQ), handler.GetTripRodReq)
	// 加载预设钓组
	transport.Handler(int(commonPB.MsgID_CMD_LOAD_TRIP_ROD_REQ), handler.LoadTripRodReq)
	// 删除杆包钓组
	transport.Handler(int(commonPB.MsgID_CMD_DEL_TRIP_ROD_REQ), handler.DelTripRodReq)
	// 更新杆包钓组
	transport.Handler(int(commonPB.MsgID_CMD_UPDATE_TRIP_ROD_REQ), handler.UpdateTripRodReq)
	// 更新杆包钓组
	transport.Handler(int(commonPB.MsgID_CMD_BATCH_UPDATE_TRIP_ROD_REQ), handler.BatchUpdateTripRodReq)
	// 保存预设钓组
	transport.Handler(int(commonPB.MsgID_CMD_SAVE_TRIP_ROD_REQ), handler.SaveTripRodReq)
	// 保存新钓组
	transport.Handler(int(commonPB.MsgID_CMD_SAVE_NEW_TRIP_ROD_REQ), handler.SaveNewTripRodReq)
	// 放入鱼杆包
	transport.Handler(int(commonPB.MsgID_CMD_PUT_TRIP_ROD_REQ), handler.PutTripRodReq)
	// 拆卸钓组
	transport.Handler(int(commonPB.MsgID_CMD_SPLIT_TRIP_ROD_REQ), handler.SplitTripRodReq)

	// 修改鱼竿耐久度
	transport.Handler(int(commonPB.MsgID_CMD_MODIFY_DURABILITY_REQ), handler.ModifyDurabilityReq)
	// 维修杆组道具
	transport.Handler(int(commonPB.MsgID_CMD_MAINTAIN_STORAGE_ITEM_REQ), handler.MaintainStorageItemReq)
	// 维修仓库道具
	transport.Handler(int(commonPB.MsgID_CMD_MAINTAIN_ROD_ITEM_REQ), handler.MaintainRodItemReq)

	// 出售道具
	transport.Handler(int(commonPB.MsgID_CMD_SELL_ITEM_REQ), handler.SellItemReq)

	// 鱼饵数据
	transport.Handler(int(commonPB.MsgID_CMD_ITEM_HEAP_REQ), handler.ItemHeapReq)

	// 临时现金购买
	transport.Handler(int(commonPB.MsgID_CMD_TEMP_CASH_BUY_REQ), handler.TempCashBuyReq)

	// 新手引导进度更新请求
	transport.Handler(int(commonPB.MsgID_CMD_HALL_UPDATE_GUIDE_PROGRESS_REQ), handler.UpdateGuideProgressReq)

	// 连续登录活动查询
	transport.Handler(int(commonPB.MsgID_CMD_HALL_CONTINUOUS_LOGIN_REQ), handler.ContinuousLogin)

	// 连续登录活动领奖
	transport.Handler(int(commonPB.MsgID_CMD_HALL_CONTINUOUS_LOGIN_REWARD_REQ), handler.ContinuousLoginReward)

	// 获取玩家所有红点
	transport.Handler(int(commonPB.MsgID_CMD_GET_PLAYER_ALL_RED_DOT_REQ), handler.GetPlayerAllRedDot)

	// 获取玩家指定模块红点
	transport.Handler(int(commonPB.MsgID_CMD_GET_PLAYER_MODULE_RED_DOT_REQ), handler.GetPlayerModuleRedDot)

	// 清除玩家指定模块红点
	transport.Handler(int(commonPB.MsgID_CMD_CLEAR_RED_DOT_REQ), handler.ClearPlayerModuleRedDot)

	// 修改玩家信息
	transport.Handler(int(commonPB.MsgID_CMD_HALL_MODIFY_PLAYER_INFO_REQ), handler.ModifyPlayerInfoReq)

	// 兑换cdk
	transport.Handler(int(commonPB.MsgID_CMD_CDKEY_EXCHANGE_REQ), handler.CDKeyExchange)

	// 局内商城请求
	transport.Handler(int(commonPB.MsgID_CMD_HALL_POND_STORE_REQ), handler.PondStoreReq)

	// 局内商城购买请求
	transport.Handler(int(commonPB.MsgID_CMD_HALL_POND_STORE_BUY_REQ), handler.PondStoreBuyReq)

	// 门票：获取当前钓场门票
	transport.Handler(int(commonPB.MsgID_CMD_HALL_GET_CUR_POND_TICKET_REQ), handler.GetCurPondTicketReq)
	// 门票：使用门票
	transport.Handler(int(commonPB.MsgID_CMD_HALL_USE_TICKET_REQ), handler.UseTicketReq)
}

func RegSupportHandler() {
	// 支持服务
	supportHandler := GetSupportInstance()
	// 实名认证
	transport.Handler(int(commonPB.MsgID_CMD_REAL_NAME_AUTH_REQ), supportHandler.RealNameAuthReq)
}
