package logicHall

import (
	"context"
	logicItem "hallsrv/internal/logic/logic_item"
	logicNotify "hallsrv/internal/logic/logic_notify"
	logicPlayer "hallsrv/internal/logic/logic_player"
	"hallsrv/internal/logic/logic_player_state"
	logicRole "hallsrv/internal/logic/logic_role"
	modelItem "hallsrv/internal/model/model_item"
	modelPlayer "hallsrv/internal/model/model_player"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/frameworks/lib/algorithm/alg_bitmap"
)

// PlayerFirstEnterHall 玩家首次进入大厅
func PlayerFirstEnterHall(ctx context.Context, playerId uint64, isReg bool) {
	// 注册
	if isReg {
		// 初始等级奖励
		SendPlayerInitLevelReward(ctx, playerId)
	}

	// TODO 这里把房间信息 玩家信息 合并返回给客户端
}

// SendPlayerInitLevelReward 发送玩家初始等级奖励
func SendPlayerInitLevelReward(ctx context.Context, playerId uint64) error {
	entry := logx.NewLogEntry(ctx)

	// 查询玩家经验值
	expNum, err := logicPlayer.QueryPlayerExpNum(ctx, playerId)
	if err != nil {
		entry.Errorf("query player:%d exp num error: %v", playerId, err)
		return err
	}

	if expNum > 0 {
		entry.Infof("player:%d exp num:%d already get init award", playerId, expNum)
		return nil
	}

	// 查询初始等级奖励数据
	initLevelMap, err := logicRole.GetExpLevelRewardFromExp(ctx, expNum)
	if err != nil || len(initLevelMap) <= 0 {
		entry.Warnf("player init level 0 reward error: %v", err)
		return err
	}

	// 查询玩家领奖信息
	expLvRwInfo, err := logic_player_state.QueryPlayerStateBmStr(ctx, playerId, modelPlayer.GetPlayerStateBmExpLvRdsField())
	if err != nil {
		entry.Errorf("query player:%d exp level reward info error:%v", playerId, err)
		return err
	}

	// 筛选没有领过的奖励
	for levelId, itemList := range initLevelMap {
		// 判断是否领奖
		isRw := alg_bitmap.InBitmap(expLvRwInfo, uint64(levelId))
		if isRw {
			// 已经领奖删除
			delete(initLevelMap, levelId)
			entry.Debugf("level up player:%d, level:%d, itemList:%s, is_rw:%v", playerId, levelId, modelItem.ItemBase2JsonStr(itemList), isRw)
			continue
		}
	}

	// 不存在没有领取的奖励
	if len(initLevelMap) <= 0 {
		entry.Debugf("player:%d not have init level reward", playerId)
		return nil
	}

	err = logicItem.SendPlayerLevelUpReward(ctx, playerId, initLevelMap)
	if err != nil {
		entry.Errorf("send player:%d init level reward error:%v", playerId, err)
		return err
	}

	// 发送通知
	logicNotify.PlayerLevelChangeNotify(ctx, playerId, initLevelMap, expNum)

	entry.Debugf("send player:%d, init lv reward size:%d, exp:%d", playerId, len(initLevelMap), expNum)

	return nil
}
