package logic_pond_store

import (
	"context"
	"fmt"
	"hallsrv/internal/config"
	"hallsrv/internal/dao/dao_pond_store"
	logicItem "hallsrv/internal/logic/logic_item"
	"hallsrv/internal/model/model_pond_store"
	"time"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	"git.keepfancy.xyz/back-end/frameworks/kit/dlm"
	"git.keepfancy.xyz/back-end/frameworks/kit/rpc/interceptor"
	"git.keepfancy.xyz/back-end/frameworks/lib/random"
	"git.keepfancy.xyz/back-end/frameworks/lib/timex"
	"github.com/sirupsen/logrus"
)

// 功能锁
func modelPlayerLock(playerId uint64) func() {
	// lockKey := config.GetRdsLockPlayerPondStore(playerId)
	// return dlm.LockKey(lockKey)
	return func() {}
}

// 全局数据锁
func modelUpdateLock() (error, func()) {
	// 使用乐观锁，抢不到则跳过，报网络异常重拉
	lockKey := config.GetRdsLockPondStore()
	getLock, unLock := dlm.DefaultLockMgr.OptimisticLockKey(lockKey)
	if !getLock {
		// 抢不到，没必要执行解锁
		return protox.CodeError(commonPB.ErrCode_ERR_OPERATION, "later update"), func() {}
	}

	return nil, unLock
}

// 查询局内商城商品数据
func QueryPondStoreGoods(ctx context.Context, channel int32, pondIds []int64) ([]*model_pond_store.TPondStore, error) {
	// 尝试查询数据
	pondStoreMap, err := dao_pond_store.QueryPondMultiStoreGood(ctx, channel, pondIds)
	if err != nil {
		return nil, err
	}

	changedPonds := make([]*model_pond_store.TPondStore, 0, len(pondIds))
	rtnList := make([]*model_pond_store.TPondStore, 0, len(pondIds))
	// 仅处理查询内容
	for _, pondId := range pondIds {
		pondStoreInfo, ok := pondStoreMap[pondId]
		// 查不出来则初始化
		if !ok {
			pondStoreInfo = model_pond_store.NewPondStore(pondId)
		}
		isChanged, err := checkPondStoreChange(ctx, pondStoreInfo)
		if err != nil {
			return nil, err
		}
		if isChanged {
			changedPonds = append(changedPonds, pondStoreInfo)
		}
		rtnList = append(rtnList, pondStoreInfo)
	}
	err, unlock := modelUpdateLock()
	if err != nil {
		return nil, err
	}
	defer unlock()

	// 回写数据
	for _, pondStoreInfo := range changedPonds {
		err = dao_pond_store.UpdatePondStoreGoods(ctx, channel, pondStoreInfo.PondId, pondStoreInfo)
		if err != nil {
			return nil, err
		}
	}
	logrus.Debugf("changedPondsGoods: %v", changedPonds)

	return rtnList, nil
}

// QueryPondStore 查询钓场商城数据
func QueryPondStore(ctx context.Context, playerId uint64, pondIds []int64) ([]*commonPB.PondStore, error) {
	entry := logx.NewLogEntry(ctx)
	_ = entry
	opts := interceptor.GetRPCOptions(ctx)
	channel := opts.ChannelType

	// 查询数据
	rtnList, err := QueryPondStoreGoods(ctx, channel, pondIds)
	if err != nil {
		return nil, err
	}
	rtn := make([]*commonPB.PondStore, 0, len(rtnList))
	for _, pond := range rtnList {
		rtn = append(rtn, pond.ToProto())
	}

	// 组装用户购买数据
	playerPondStore, err := dao_pond_store.QueryPlayerPondStore(ctx, playerId, pondIds)
	if err != nil {
		return nil, err
	}
	for _, pond := range rtn {
		playerPondStoreInfo, ok := playerPondStore[pond.PondId]
		if !ok {
			continue
		}
		for _, batch := range pond.List {
			playerPondBought, ok := playerPondStoreInfo.Bought[batch.BatchId]
			if !ok {
				continue
			}
			for _, goods := range batch.List {
				bought, ok := playerPondBought.Bought[goods.GoodId]
				if !ok {
					continue
				}
				goods.Bought = bought
			}
		}
	}

	return rtn, nil
}

// 检查钓场商城数据是否变化
func checkPondStoreChange(ctx context.Context, pondStoreInfo *model_pond_store.TPondStore) (bool, error) {
	now := timex.Now()
	isChanged := false
	pondId := pondStoreInfo.PondId
	pondStoreCfgs := GetPondStoreCfg(ctx, pondId)
	for _, pondStoreCfg := range pondStoreCfgs {
		batchChange := false
		// 检查batch有效性
		batchId := pondStoreCfg.Id
		// batch不存在
		batchInfo, ok := pondStoreInfo.PondBatches[batchId]
		if ok {
			// 时间校验不一致
			if batchInfo.TsStart > now.Unix() || batchInfo.TsEnd < now.Unix() {
				batchChange = true
			}
		} else {
			batchChange = true
		}
		// 场次不需要重复校验生成
		if !batchChange {
			continue
		}
		delete(pondStoreInfo.PondBatches, batchId)

		// 生成配置时间
		tsStart, tsEnd, err := GetFlushTime(ctx, int64(pondStoreCfg.TimeType), pondStoreCfg.TsStart, pondStoreCfg.TsEnd)
		if err != nil {
			return false, err
		}

		logrus.Debugf("batchid:%d, now: %v, tsStart: %v, tsEnd: %v", batchId, now, time.Unix(tsStart, 0), time.Unix(tsEnd, 0))
		// 校验是否有效期内
		if now.Unix() < tsStart || now.Unix() > tsEnd {
			continue
		}
		isChanged = true

		newBatchInfo := &model_pond_store.PondBatch{
			BatchId: batchId,
			TsStart: tsStart,
			TsEnd:   tsEnd,
		}
		// 生成商品
		err = flushPondStoreGoods(ctx, newBatchInfo, pondStoreCfg)
		if err != nil {
			return false, err
		}
		pondStoreInfo.PondBatches[batchId] = newBatchInfo
	}

	return isChanged, nil
}

func GetFlushTime(ctx context.Context, tyimeType int64, tsStart int64, tsEnd int64) (int64, int64, error) {
	worktime := timex.NewWorkTimeModel(timex.TIME_MODEL_REAL)
	now := timex.Now()
	start, end, err := worktime.GetFlushDuration(now, tyimeType, tsStart, tsEnd)
	if err != nil {
		return 0, 0, err
	}
	return start, end, nil
}

// 刷新场次商品数据
func flushPondStoreGoods(ctx context.Context, batchInfo *model_pond_store.PondBatch, pondStoreCfg *cmodel.PondStore) error {
	// 已存在道具列表
	goodsMap := make(map[int64]struct{})
	goodsList := make([]*model_pond_store.PoodGood, 0, 4)
	for _, groupInfo := range pondStoreCfg.Groups {
		goods, err := filterGoods(ctx, groupInfo.GroupId, groupInfo.Num, goodsMap)
		if err != nil {
			return err
		}
		goodsList = append(goodsList, goods...)
	}
	batchInfo.PondGoods = goodsList
	return nil
}

// 筛选商品
func filterGoods(ctx context.Context, groupId int64, nums int32, alreadyKey map[int64]struct{}) ([]*model_pond_store.PoodGood, error) {
	groupCfg := GetPondStoreCfgGroup(ctx, groupId)
	if len(groupCfg) == 0 {
		logrus.Errorf("pond store group %d not found", groupId)
		return nil, protox.CodeError(commonPB.ErrCode_ERR_CONF_ERROR, fmt.Sprintf("pond store group %d not found", groupId))
	}

	goodsList := make([]*model_pond_store.PoodGood, 0, nums)
	for nums > 0 {
		// TODO: 缓存商品
		goodsWeight := make(map[int64]int32)
		for _, cfg := range groupCfg {
			if _, ok := alreadyKey[cfg.GoodsId]; ok {
				continue
			}
			goodsWeight[cfg.Id] = int32(cfg.ItemWeight)
		}
		// 已经没有可选的
		if len(goodsWeight) == 0 {
			return goodsList, nil
		}

		goodsIndex := random.RandForInt64Weight(goodsWeight)
		cfg := groupCfg[goodsIndex]
		goods := &model_pond_store.PoodGood{
			GoodsId:  cfg.GoodsId,
			Nums:     cfg.Nums,
			Price:    int32(cfg.CostCount),
			CostItem: cfg.CostItem,
		}
		// 折扣
		if len(cfg.DctWgt) == 0 {
			goods.Discount = 0
		} else {
			if len(cfg.DctWgt) == 1 {
				goods.Discount = cfg.DctWgt[0].Discount
			} else {
				discountMap := make(map[int64]int32)
				for i, dctWgt := range cfg.DctWgt {
					discountMap[int64(i)] = int32(dctWgt.Weight)
				}
				discountIndex := random.RandForInt64Weight(discountMap)
				goods.Discount = cfg.DctWgt[discountIndex].Discount
			}
		}

		alreadyKey[cfg.GoodsId] = struct{}{}
		goodsList = append(goodsList, goods)
		nums--
	}

	return goodsList, nil
}

// BuyPondStoreGoods 购买钓场商城商品
func BuyPondStoreGoods(ctx context.Context, playerId uint64, pondId int64, batchId int64, goodsId int64, buyNums int64) (*model_pond_store.PondStoreBuyVo, error) {
	unlock := modelPlayerLock(playerId)
	defer unlock()
	entry := logx.NewLogEntry(ctx)
	// 查询商品配置
	goodsCfg := cmodel.GetGoodsBasic(goodsId, consul_config.WithGrpcCtx(ctx))
	if goodsCfg == nil {
		return nil, protox.CodeError(commonPB.ErrCode_ERR_CONF_ERROR, fmt.Sprintf("goods cfg:%d not found", goodsId))
	}
	op := interceptor.GetRPCOptions(ctx)
	channel := op.ChannelType

	now := timex.Now()
	// 查询道具存在性
	pondStoreInfo, err := dao_pond_store.QueryPondMultiStoreGood(ctx, channel, []int64{pondId})
	if err != nil {
		return nil, err
	}
	pondInfo, ok := pondStoreInfo[pondId]
	if !ok {
		return nil, protox.CodeError(commonPB.ErrCode_ERR_OPERATION, fmt.Sprintf("pond %d not found", pondId))
	}
	batchInfo, ok := pondInfo.PondBatches[batchId]
	if !ok {
		return nil, protox.CodeError(commonPB.ErrCode_ERR_OPERATION, fmt.Sprintf("batch %d not found", batchId))
	}

	// 过期检查
	if batchInfo.TsEnd < now.Unix() || batchInfo.TsStart > now.Unix() {
		return nil, protox.CodeError(commonPB.ErrCode_ERR_OPERATION, fmt.Sprintf("batch %d expired", batchId))
	}

	var buyGoods *model_pond_store.PoodGood
	for _, goods := range batchInfo.PondGoods {
		if goods.GoodsId == goodsId {
			buyGoods = goods
			break
		}
	}
	if buyGoods == nil {
		return nil, protox.CodeError(commonPB.ErrCode_ERR_OPERATION, fmt.Sprintf("goods %d not found", goodsId))
	}

	// 查询用户数据
	playerPondStore, err := dao_pond_store.QueryPlayerPondStore(ctx, playerId, []int64{pondId})
	if err != nil {
		return nil, err
	}
	playerPondStoreInfo, ok := playerPondStore[pondId]
	if !ok {
		playerPondStoreInfo = model_pond_store.NewPondStoreBuy(pondId)
	}
	playerPondBought, ok := playerPondStoreInfo.Bought[batchId]
	if !ok {
		playerPondStoreInfo.Bought[batchId] = model_pond_store.PlayerPondBought{
			BatchId: batchId,
			Bought:  make(map[int64]int32),
			TsEnd:   batchInfo.TsEnd,
			TsStart: batchInfo.TsStart,
		}
		playerPondBought = playerPondStoreInfo.Bought[batchId]
	}

	playerBought := playerPondBought.Bought[goodsId]
	// 处理扣除规则
	if playerBought+int32(buyNums) > buyGoods.Nums {
		// 可购买次数不足
		return nil, protox.CodeError(commonPB.ErrCode_ERR_OPERATION, fmt.Sprintf("goods %d not enough", goodsId))
	}

	playerBought += int32(buyNums)
	playerPondBought.Bought[goodsId] = playerBought

	// 计算折扣
	price := (int64(buyGoods.Price) * int64(buyGoods.Discount) * int64(buyNums)) / 100
	costItem := buyGoods.CostItem
	lossItem := []*commonPB.ItemBase{
		{
			ItemId:    costItem,
			ItemCount: price,
		},
	}
	// 更新数据标记
	err = dao_pond_store.UpdatePlayerPondStore(ctx, playerId, pondId, playerPondStoreInfo)
	if err != nil {
		return nil, err
	}

	// 扣除
	_, err = logicItem.OperatePlayerItem(ctx, playerId, lossItem, commonPB.ITEM_OPERATION_IO_REDUCE, commonPB.ITEM_SOURCE_TYPE_IST_POND_STORE_BUY, commonPB.STORAGE_TYPE_ST_STORE, false)
	if err != nil {
		return nil, err
	}

	rewardItem := []*commonPB.ItemBase{
		{
			ItemId:    goodsCfg.ItemId,
			ItemCount: goodsCfg.ItemCount * int64(buyNums),
		},
	}

	// 发奖
	reward, err := logicItem.OperatePlayerItem(ctx, playerId, rewardItem, commonPB.ITEM_OPERATION_IO_ADD, commonPB.ITEM_SOURCE_TYPE_IST_POND_STORE_BUY, commonPB.STORAGE_TYPE_ST_STORE, false)
	if err != nil {
		return nil, err
	}
	entry.Debugf("buy goods %d, buyNums: %d, reward: %v", goodsId, buyNums, reward)

	return &model_pond_store.PondStoreBuyVo{
		PondGood: buyGoods,
		Reward:   reward,
		Bought:   playerBought,
	}, nil
}
