package logic_pond_store

import (
	"context"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
)

// TODO: 补全预处理+懒加载模式

// GetPondStoreCfg 获取钓场商城配置
func GetPondStoreCfg(ctx context.Context, pondId int64) []*cmodel.PondStore {
	pondStoreAllCfg := cmodel.GetAllPondStore(consul_config.WithGrpcCtx(ctx))
	// 过滤ponid
	list := make([]*cmodel.PondStore, 0)
	for _, pondStore := range pondStoreAllCfg {
		if pondStore.PondId == pondId {
			list = append(list, pondStore)
		}
	}

	return list
}

// GetPondStoreCfgGroup 获取钓场商城配置组
func GetPondStoreCfgGroup(ctx context.Context, groupId int64) map[int64]*cmodel.PondStoreGroup {
	groupCfgs := cmodel.GetAllPondStoreGroup(consul_config.WithGrpcCtx(ctx))
	// 过滤groupId
	list := make(map[int64]*cmodel.PondStoreGroup)
	for _, groupCfg := range groupCfgs {
		if groupCfg.Group == groupId {
			list[groupCfg.Id] = groupCfg
		}
	}
	return list
}
