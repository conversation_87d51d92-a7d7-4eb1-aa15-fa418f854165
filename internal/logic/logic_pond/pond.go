package logicPond

import (
	"context"
	logicItem "hallsrv/internal/logic/logic_item"
	logicPlayer "hallsrv/internal/logic/logic_player"
	modelItem "hallsrv/internal/model/model_item"
	rpcTrip "hallsrv/internal/repo/rpc_trip"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	"git.keepfancy.xyz/back-end/frameworks/lib/transform"
)

// PlayerEntryPond 玩家进入钓场 需要校验是否满足扣费等
func PlayerEntryPond(ctx context.Context, playerId uint64, pondId int64, spotId int32, gameType commonPB.GAME_TYPE, roomType commonPB.ROOM_TYPE) (*commonPB.RoomInfo, commonPB.ErrCode) {
	entry := logx.NewLogEntry(ctx)
	if playerId == 0 || pondId <= 0 {
		entry.Errorf("player:%d, pond:%d error", playerId, pondId)
		return nil, commonPB.ErrCode_ERR_BAD_PARAM
	}

	// 查询玩家是否已经在房间中 如果已经在房间中 不进行下面的一系列校验
	roomInfo, err := rpcTrip.RpcGetPlayerRoomInfo(ctx, playerId)
	if err != nil {
		entry.Errorf("get player:%d, room info error:%+v, pond:%d error", playerId, err, pondId)
		return nil, commonPB.ErrCode_ERR_HALL_GET_ROOM
	}

	// 玩家已经在房间中
	if roomInfo != nil && roomInfo.RoomId != "" {
		entry.Infof("player:%d already in room, pondId:%d, roomInfo:%v", playerId, pondId, roomInfo.String())
		roomInfo.SpotId = spotId
		return roomInfo, commonPB.ErrCode_ERR_HALL_EXIST_IN_ROOM
	}

	pondConf := cmodel.GetFishPondList(pondId, consulconfig.WithGrpcCtx(ctx))
	if pondConf == nil {
		entry.Errorf("pond:%d not exist", pondId)
		return nil, commonPB.ErrCode_ERR_CONF_ERROR
	}

	// 查询玩家等级
	expLevel, err := logicPlayer.QueryPlayerExpLevel(ctx, playerId)
	if err != nil {
		entry.Errorf("player:%d query player level error:%v", playerId, err)
		return nil, commonPB.ErrCode_ERR_HALL_ENTRY_LEVEL
	}

	// 等级不足
	if expLevel < pondConf.EntryLevel {
		entry.Errorf("player:%d level:%d not enough, pondId:%d", playerId, expLevel, pondId)
		return nil, commonPB.ErrCode_ERR_HALL_ENTRY_LEVEL
	}

	// 判断钓点是否可开启
	if spotId > 0 {
		// 钓点关闭
		if !transform.Int32SliceContain(pondConf.OpenSpot, spotId) {
			entry.Errorf("player:%d choose spotId:%d not open", playerId, spotId)
			return nil, commonPB.ErrCode_ERR_HALL_SPOT_CLOSE
		}
	}

	// 扣费
	if pondConf.EntryFee > 0 && pondConf.EntryItem > 0 {
		costItemList, err := modelItem.NewItemOptParam(ctx, pondConf.EntryItem, pondConf.EntryFee, false)
		if len(costItemList) == 0 || err != nil {
			entry.Errorf("deduct entry fee, cost item:%d, num:%d is error:%v", pondConf.EntryItem, pondConf.EntryFee, err)
			return nil, commonPB.ErrCode_ERR_BAD_PARAM
		}

		// 消耗道具信息
		costItemBase := []*commonPB.ItemBase{
			{
				ItemId:    pondConf.EntryItem,
				ItemCount: pondConf.EntryFee,
			},
		}

		// 扣除玩家道具
		rewordInfo, err := logicItem.OperatePlayerItem(ctx, playerId, costItemBase, commonPB.ITEM_OPERATION_IO_REDUCE, commonPB.ITEM_SOURCE_TYPE_IST_ENTRY_POND_FEE, commonPB.STORAGE_TYPE_ST_STORE, true)

		if err != nil || rewordInfo == nil {
			entry.Errorf("player:%d buy store goods, cost item:%d, num:%d is error:%v", playerId, pondConf.EntryItem, pondConf.EntryFee, err)
			return nil, commonPB.ErrCode_ERR_HALL_ITEM_NOT_ENOUGH
		}
	}

	// 进房
	roomInfo, err = rpcTrip.RpcJoinTrip(ctx, playerId, pondId, gameType, roomType)
	if err != nil || roomInfo == nil {
		entry.Errorf("player:%d, pondId:%d, gameType:%d, roomType:%d, enter error:%v", playerId, pondId, gameType, roomType, err)
		return nil, commonPB.ErrCode_ERR_HALL_ENTER_SPOT
	}

	roomInfo.SpotId = spotId

	entry.Debugf("player:%d, pondId:%d, gameType:%d, spotId:%d, roomType:%d, enter success, roomInfo:%s", playerId, pondId, spotId, gameType, roomType, roomInfo.String())

	return roomInfo, commonPB.ErrCode_ERR_SUCCESS
}
