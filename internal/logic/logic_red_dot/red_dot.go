package logic

import (
	"context"
	logicNotify "hallsrv/internal/logic/logic_notify"
	"hallsrv/internal/logic/logic_player_state"
	modelPlayer "hallsrv/internal/model/model_player"

	hallPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/hall"
	"git.keepfancy.xyz/back-end/frameworks/lib/algorithm/alg_bitmap"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"

	modelReddot "hallsrv/internal/model/model_red_dot"
)

// SetRedDot 设置红点
func SetRedDot(ctx context.Context, playerID uint64, moduleType commonPB.USER_MODULE_TYPE, subModuleType []modelReddot.SubModuleType) error {
	entry := logx.NewLogEntry(ctx)

	// 查询红点数据
	bitmapStr, err := logic_player_state.QueryPlayerStateBmStr(ctx, playerID, modelPlayer.GetPlayerStateBmRedDotRdsField())
	if err != nil {
		entry.Errorf("get bitmap failed: playerID=%d, err=%v", playerID, err)
		return err
	}

	posList := make([]uint64, 0, len(subModuleType))
	newBitmapStr := bitmapStr
	for _, sub := range subModuleType {
		// 计算红点在bitmap中的位置
		pos := modelReddot.CalcPosition(moduleType, sub)
		posList = append(posList, uint64(pos))
		// 设置位
		newBitmapStr, err = alg_bitmap.AddBit(newBitmapStr, uint64(pos))
		if err != nil {
			entry.Errorf("set bitmap failed: playerID=%d, pos=%d, err=%v", playerID, pos, err)
			return err
		}
	}

	// 没变化不用更新
	if newBitmapStr == bitmapStr {
		entry.Debugf("【Set Red Dot Success】no diff playerID=%d, module=%d, subModule=%+v",
			playerID, moduleType, subModuleType)
		return nil
	}

	if err = logic_player_state.UpdatePlayerStateBmStr(ctx, playerID, modelPlayer.GetPlayerStateBmRedDotRdsField(), newBitmapStr); err != nil {
		entry.Errorf("save bitmap failed: playerID=%d, moduleType=%d, subModuleType=%+v, err=%v",
			playerID, moduleType, subModuleType, err)
		return err
	}

	// 推送给客户端
	logicNotify.PlayerRedDotNotify(ctx, playerID, modelReddot.BuildRedDotInfos(posList))

	entry.Debugf("【Set Red Dot Success】playerID=%d, module=%d, subModule=%d",
		playerID, moduleType, subModuleType)
	return nil
}

// ClearRedDot 清除红点
func ClearRedDot(ctx context.Context, playerID uint64, moduleType commonPB.USER_MODULE_TYPE, subModuleType modelReddot.SubModuleType) (*hallPB.RedDotInfo, error) {
	entry := logx.NewLogEntry(ctx)

	// 查询红点数据
	bitmapStr, err := logic_player_state.QueryPlayerStateBmStr(ctx, playerID, modelPlayer.GetPlayerStateBmRedDotRdsField())
	if err != nil {
		entry.Errorf("get bitmap failed: playerID=%d, err=%v", playerID, err)
		return nil, err
	}

	// 计算红点在bitmap中的位置
	pos := modelReddot.CalcPosition(moduleType, subModuleType)

	// 重置位
	newBitmapStr, err := alg_bitmap.RemoveBit(bitmapStr, pos)
	if err != nil {
		entry.Errorf("reset bitmap failed: playerID=%d, pos=%d, err=%v", playerID, pos, err)
		return nil, err
	}
	dot := &hallPB.RedDotInfo{
		ModuleType:    moduleType,
		SubModuleType: int32(subModuleType),
		HasRedDot:     false,
	}

	// 没变化不用更新
	if newBitmapStr == bitmapStr {
		entry.Debugf("【clear Red Dot Success】no diff playerID=%d, module=%d, subModule=%+v",
			playerID, moduleType, subModuleType)
		return dot, nil
	}

	if err = logic_player_state.UpdatePlayerStateBmStr(ctx, playerID, modelPlayer.GetPlayerStateBmRedDotRdsField(), newBitmapStr); err != nil {
		entry.Errorf("save bitmap failed: playerID=%d, moduleType=%d, subModuleType=%+v, err=%v",
			playerID, moduleType, subModuleType, err)
		return nil, err
	}

	entry.Debugf("【Clear Red Dot Success】playerID=%d, module=%d, subModule=%d",
		playerID, moduleType, subModuleType)
	return dot, nil
}

// GetPlayerAllRedDots 获取玩家所有红点
func GetPlayerAllRedDots(ctx context.Context, playerID uint64) ([]*hallPB.RedDotInfo, error) {
	entry := logx.NewLogEntry(ctx)

	// 查询红点数据
	bitmapStr, err := logic_player_state.QueryPlayerStateBmStr(ctx, playerID, modelPlayer.GetPlayerStateBmRedDotRdsField())
	if err != nil {
		entry.Errorf("get bitmap failed: playerID=%d, err=%v", playerID, err)
		return nil, err
	}

	// 获取所有设置的位
	positions, err := alg_bitmap.GetMarkedBits(bitmapStr)
	if err != nil {
		entry.Errorf("traverse bitmap failed: playerID=%d, err=%v", playerID, err)
		return nil, err
	}

	result := modelReddot.BuildRedDotInfos(positions)

	entry.Debugf("【Get All Red Dots】playerID=%d, total=%d, enabled=%d",
		playerID, len(positions), len(result))
	return result, nil
}

// GetPlayerModuleRedDots 获取玩家特定模块的红点
func GetPlayerModuleRedDots(ctx context.Context, playerID uint64, moduleType commonPB.USER_MODULE_TYPE) ([]*hallPB.RedDotInfo, error) {
	entry := logx.NewLogEntry(ctx)

	// 先获取所有红点
	allRedDots, err := GetPlayerAllRedDots(ctx, playerID)
	if err != nil {
		return nil, err
	}

	// 过滤出指定模块的红点
	result := make([]*hallPB.RedDotInfo, 0)
	for _, redDot := range allRedDots {
		if redDot.ModuleType == moduleType {
			result = append(result, redDot)
		}
	}

	entry.Debugf("【Get Module Red Dots】playerID=%d, module=%d, count=%d",
		playerID, moduleType, len(result))
	return result, nil
}
