package logic_cdk

import (
	"context"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	hallPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/hall"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/frameworks/kit/dlm"
	"git.keepfancy.xyz/back-end/frameworks/lib/algorithm/alg_bitmap"
	"hallsrv/internal/config"
	"hallsrv/internal/dao/dao_cdk"
	logicItem "hallsrv/internal/logic/logic_item"
	"hallsrv/internal/model/model_cdk" // Used for model_cdk.CdkBatchRecordResult
	"strings"
)

// CDKeyExchange 处理CDK兑换逻辑
func CDKeyExchange(ctx context.Context, playerID uint64, req *hallPB.CDKeyExchangeReq) (reward *commonPB.Reward, err error) {
	entry := logx.NewLogEntry(ctx)

	cdkCode := strings.TrimSpace(req.GetCdKey())
	if cdkCode == "" {
		entry.Warnf("invalid cdk code: %s", cdkCode)
		return nil, protox.CodeError(commonPB.ErrCode_ERR_BAD_PARAM, "CDK code '%s' is invalid.", cdkCode)
	}

	// 加锁防止并发兑换
	unlock := dlm.LockKey(config.CdkLockKey(cdkCode))
	defer unlock()

	entry.Infof("player %d attempting to exchange CDK: %s", playerID, cdkCode)

	// 获取cdk信息
	dbRes, err := dao_cdk.GetCdkBatchInfoByCdk(ctx, cdkCode)
	if err != nil {
		entry.Warnf("cdk not found: %s, err: %v", cdkCode, err)
		return nil, protox.CodeError(commonPB.ErrCode_ERR_HALL_CDKEY_EXCHANGE_FAILED, "CDK not found")
	}

	// 校验CDK状态
	if err = model_cdk.ValidateCdk(dbRes, playerID); err != nil {
		entry.Warnf("cdk validation failed: player=%d cdk=%s err=%v", playerID, cdkCode, err)
		return nil, err
	}

	// 从缓存中获取的CDK信息进行二次校验
	if dbRes.DoubleCheck {
		dbRes, err = dao_cdk.GetCdkBatchInfoFromDB(ctx, cdkCode)
		if err != nil {
			entry.Errorf("failed to get fresh cdk info: cdk=%s err=%v", cdkCode, err)
			return nil, protox.CodeError(commonPB.ErrCode_ERR_HALL_CDKEY_EXCHANGE_FAILED, "Failed to verify CDK status")
		}

		// 使用最新数据进行最终校验
		if err = model_cdk.ValidateCdk(dbRes, playerID); err != nil {
			entry.Warnf("fresh cdk validation failed: player=%d cdk=%s err=%v", playerID, cdkCode, err)
			return nil, err
		}
	}

	// 进入领取流程
	usedBm, err := alg_bitmap.AddBit(dbRes.UsedBm, playerID)
	if err != nil {
		entry.Errorf("bitmap add failed: player=%d cdk=%s err=%v", playerID, cdkCode, err)
		return nil, protox.CodeError(commonPB.ErrCode_ERR_HALL_CDKEY_EXCHANGE_FAILED, "AddBit failed")
	}

	// 删除缓存并更新记录
	err = dao_cdk.UpdateCdkRecordUsage(ctx, cdkCode, usedBm)
	if err != nil {
		entry.Errorf("update usage failed: cdk=%s err=%v", cdkCode, err)
		return nil, protox.CodeError(commonPB.ErrCode_ERR_HALL_CDKEY_EXCHANGE_FAILED, "UpdateCdkRecordUsage failed")
	}

	// 解析奖励
	awardList, err := dbRes.GetRewards()
	if err != nil {
		entry.Errorf("parse rewards failed: cdk=%s err=%v", cdkCode, err)
		return nil, protox.CodeError(commonPB.ErrCode_ERR_HALL_CDKEY_EXCHANGE_FAILED, "Failed to parse rewards for CDK")
	}

	// 发奖
	reward, err = logicItem.OperatePlayerItem(ctx, playerID, awardList, commonPB.ITEM_OPERATION_IO_ADD,
		commonPB.ITEM_SOURCE_TYPE_IST_CDK_REWARD, commonPB.STORAGE_TYPE_ST_STORE, true)
	if err != nil {
		entry.Errorf("operate item failed: player=%d cdk=%s err=%v", playerID, cdkCode, err)
		return nil, protox.CodeError(commonPB.ErrCode_ERR_HALL_CDKEY_EXCHANGE_FAILED, "OperatePlayerItem failed")
	}

	entry.Infof("player %d successfully exchanged CDK: %s", playerID, cdkCode)
	return reward, nil
}
