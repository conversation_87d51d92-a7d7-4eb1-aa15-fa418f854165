package logic_cdk

import (
	"context"
	"errors"
	"fmt"
	"hallsrv/internal/dao/dao_cdk"
	"hallsrv/internal/model/model_cdk"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	gmPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/gm"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	"git.keepfancy.xyz/back-end/frameworks/lib/random"
)

// CreateCDKBatchWithCodes 创建CDK批次和对应的CDK码
func CreateCDKBatchWithCodes(ctx context.Context, input *gmPB.GmCmdCreateCDKReq) (*gmPB.GmCmdCreateCDKRsp, error) {
	entry := logx.NewLogEntry(ctx)

	// 输入参数校验
	if input.StartTime >= input.EndTime {
		return nil, errors.New("end_time must be greater than start_time")
	}
	if len(input.Rewards) == 0 {
		return nil, errors.New("rewards list cannot be empty")
	}
	if input.GenerationCount > 100000 {
		return nil, errors.New("generation_count cannot be greater than 100000")
	}

	itemInfo := cmodel.GetAllItem(consul_config.WithGrpcCtx(ctx))
	// 检验道具是否存在
	for _, reward := range input.Rewards {
		if reward.ItemId == 0 {
			return nil, errors.New("item_id cannot be 0")
		}
		if _, ok := itemInfo[reward.ItemId]; !ok {
			return nil, fmt.Errorf("item_id %d not found", reward.ItemId)
		}
	}

	// 生成CDK
	var cdkCodesToCreate []string
	switch input.GenerationOption {
	case commonPB.CDK_GENERATION_OPTION_CGO_RANDOM:
		if input.GenerationCount <= 0 {
			return nil, errors.New("generation_count must be > 0 for random generation")
		}
		var genErr error
		cdkCodesToCreate, genErr = random.NewCDKeyGenerator(12).GenerateBatch(int(input.GenerationCount))
		if genErr != nil {
			return nil, fmt.Errorf("failed to generate CDK codes: %s", genErr)
		}
	case commonPB.CDK_GENERATION_OPTION_CGO_CUSTOM:
		cdkCodesToCreate = input.ManualCdks
	default:
		return nil, fmt.Errorf("invalid GenerationOption: %d", input.GenerationOption)
	}
	if len(cdkCodesToCreate) == 0 {
		return nil, errors.New("no CDK codes to create (count is zero or manual list is empty)")
	}

	// 创建CDKBatch对象
	cdkBatch := model_cdk.ToCDKBatch(input)
	cdkBatch.CDKCount = int32(len(cdkCodesToCreate))
	cdkRecords := model_cdk.CreateCDKRecords(cdkCodesToCreate)

	//  调用 DAO 层函数执行数据库操作
	err := dao_cdk.CreateCDKBatchWithRecords(ctx, cdkBatch, cdkRecords)
	if err != nil {
		entry.Errorf("create batch failed: err=%v", err)
		return nil, fmt.Errorf("failed to create CDK batch and associated codes: %w", err)
	}
	rsp := &gmPB.GmCmdCreateCDKRsp{
		Ret: &commonPB.Result{
			Code: commonPB.ErrCode_ERR_SUCCESS,
			Desc: "CDK batch created successfully",
		},
	}
	return rsp, nil
}

// QueryCDKBatches CDK批次查询接口（分页）
func QueryCDKBatches(ctx context.Context, input *gmPB.GmCmdQueryCDKBatchesReq) (*gmPB.GmCmdQueryCDKBatchesRsp, error) {
	entry := logx.NewLogEntry(ctx)

	// 转换请求参数
	req := model_cdk.FromGmQueryCDKBatchesReq(input)
	if req == nil {
		return nil, errors.New("invalid request parameters")
	}

	// 参数验证
	if req.Page < 1 {
		return nil, errors.New("page must be greater than 0")
	}
	if req.PageSize < 1 || req.PageSize > 1000 {
		return nil, errors.New("page_size must be between 1 and 1000")
	}

	// 调用 DAO 层查询
	batches, total, err := dao_cdk.QueryCDKBatches(ctx, req.Page, req.PageSize, int32(req.Status))
	if err != nil {
		entry.Errorf("query batches failed: err=%v", err)
		return nil, fmt.Errorf("failed to query CDK batches: %w", err)
	}

	response := &model_cdk.QueryCDKBatchesResponse{
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
		Batches:  batches,
	}

	// 转换响应
	gmResponse := model_cdk.ToGmQueryCDKBatchesRsp(response)
	return gmResponse, nil
}

// QueryCDKRecords CDK记录查看接口
func QueryCDKRecords(ctx context.Context, input *gmPB.GmCmdQueryCDKRecordsReq) (*gmPB.GmCmdQueryCDKRecordsRsp, error) {
	entry := logx.NewLogEntry(ctx)

	// 转换请求参数
	req := model_cdk.FromGmQueryCDKRecordsReq(input)
	if req == nil {
		return nil, errors.New("invalid request parameters")
	}

	// 参数验证
	if req.BatchID == 0 {
		return nil, errors.New("batch_id is required")
	}
	if req.Page < 1 {
		return nil, errors.New("page must be greater than 0")
	}
	if req.PageSize < 1 || req.PageSize > 10000 {
		return nil, errors.New("page_size must be between 1 and 10000")
	}

	// 调用 DAO 层查询
	records, total, err := dao_cdk.QueryCDKRecords(ctx, req.BatchID, req.Page, req.PageSize)
	if err != nil {
		entry.Errorf("query records failed: batch=%d err=%v", req.BatchID, err)
		return nil, fmt.Errorf("failed to query CDK records: %w", err)
	}

	response := &model_cdk.QueryCDKRecordsResponse{
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
		Records:  model_cdk.BatchToCDKRecordInfos(records),
	}

	// 转换响应
	gmResponse := model_cdk.ToGmQueryCDKRecordsRsp(response)
	return gmResponse, nil
}

// DisableCDKBatch CDK批次作废接口
func DisableCDKBatch(ctx context.Context, input *gmPB.GmCmdDisableCDKBatchReq) (*gmPB.GmCmdDisableCDKBatchRsp, error) {
	entry := logx.NewLogEntry(ctx)

	// 转换请求参数
	req := model_cdk.FromGmDisableCDKBatchReq(input)
	if req == nil {
		return nil, errors.New("invalid request parameters")
	}

	// 参数验证
	if req.BatchID == 0 {
		return nil, errors.New("batch_id is required")
	}

	// 调用 DAO 层作废批次
	err := dao_cdk.DisableCDKBatch(ctx, req.BatchID)
	if err != nil {
		entry.Errorf("disable batch failed: id=%d err=%v", req.BatchID, err)
		response := &model_cdk.DisableCDKBatchResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to disable CDK batch: %v", err),
		}
		return model_cdk.ToGmDisableCDKBatchRsp(response), nil
	}

	response := &model_cdk.DisableCDKBatchResponse{
		Success: true,
		Message: fmt.Sprintf("CDK batch %d has been successfully disabled", req.BatchID),
	}

	// 转换响应
	gmResponse := model_cdk.ToGmDisableCDKBatchRsp(response)
	return gmResponse, nil
}
