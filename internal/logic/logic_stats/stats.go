package logic_stats

import (
	"context"
	"fmt"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/rpc_biz/crpc_stats"
)

// GetStatsRules 获取统计规则
func GetStatsRules(ctx context.Context, playerId uint64, list []*commonPB.StatsRuleDesc) ([]*commonPB.StatsRuleInfo, error) {

	// 封印-1查询
	err := filterStatsRules(list)
	if err != nil {
		return nil, err
	}

	rules, err := crpc_stats.RpcGetStatsRules(ctx, playerId, list)
	if err != nil {
		return nil, err
	}

	return rules, nil
}

func filterStatsRules(rules []*commonPB.StatsRuleDesc) error {
	var checkVal int64 = 0
	for _, rule := range rules {
		if rule.GetTyp() == int32(checkVal) {
			return fmt.Errorf("typ is 0")
		}
	}
	return nil
}
