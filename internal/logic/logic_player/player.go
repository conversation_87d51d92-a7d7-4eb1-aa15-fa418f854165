package logicPlayer

import (
	"context"
	"fmt"
	"strings"

	logicRole "hallsrv/internal/logic/logic_role"
	rpcAsset "hallsrv/internal/repo/rpc_asset"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/frameworks/kit/rpc/interceptor"
)

// QueryPlayerInfo 查询玩家基础信息
func QueryPlayerInfo(ctx context.Context, playerId uint64) (*commonPB.PlayerBaseInfo, error) {
	entry := logx.NewLogEntry(ctx)
	playerInfo := &commonPB.PlayerBaseInfo{
		PlayerId: playerId,
	}

	if playerId == 0 {
		entry.Errorf("query player info error: playerId is 0")
		return playerInfo, fmt.Errorf("playerId is 0")
	}

	// 先查询玩家资产信息
	categoryList := []commonPB.ITEM_CATEGORY{
		commonPB.ITEM_CATEGORY_IC_CURRENCY,
	}

	itemList, err := rpcAsset.GetPlayerCategoryListItemInfo(ctx, playerId, interceptor.GetRPCOptions(ctx).ProductId, categoryList, commonPB.STORAGE_TYPE_ST_STORE)
	if err != nil {
		entry.Errorf("get player category list:%v item info error: %v", categoryList, err)
		return playerInfo, err
	}

	// 赋值玩家金币 钻石 和经验值
	for _, item := range itemList {
		switch item.GetItem().GetItemType() {
		case commonPB.ITEM_TYPE_IT_CURRENCY_COIN:
			playerInfo.Coins = item.ItemCount
		case commonPB.ITEM_TYPE_IT_CURRENCY_DIAMOND:
			playerInfo.Diamond = item.ItemCount
		case commonPB.ITEM_TYPE_IT_CURRENCY_EXP:
			playerInfo.ExpNum = item.ItemCount
		default:
		}
	}

	// 计算等级
	expLevel, _, err := logicRole.CalcExpLevelFromExp(ctx, playerInfo.ExpNum, 0)
	if err == nil {
		playerInfo.ExpLevel = expLevel
	}

	// TODO 查询其他基础信息

	entry.Debugf("query player:%d info success, playerInfo:%s", playerId, playerInfo.String())

	return playerInfo, nil
}

// QueryPlayerExpNum 查询玩家经验值
func QueryPlayerExpNum(ctx context.Context, playerId uint64) (int64, error) {
	// 先查询玩家资产信息
	entry := logx.NewLogEntry(ctx)
	categoryList := []commonPB.ITEM_CATEGORY{
		commonPB.ITEM_CATEGORY_IC_CURRENCY,
	}

	itemList, err := rpcAsset.GetPlayerCategoryListItemInfo(ctx, playerId, interceptor.GetRPCOptions(ctx).ProductId, categoryList, commonPB.STORAGE_TYPE_ST_STORE)
	if err != nil {
		entry.Errorf("get player category list:%v item info error: %v", categoryList, err)
		return 0, err
	}

	// 赋值玩家金币 钻石 和经验值
	for _, item := range itemList {
		if item.GetItem().GetItemType() == commonPB.ITEM_TYPE_IT_CURRENCY_EXP {
			return item.ItemCount, nil
		}
	}

	return 0, nil
}

// QueryPlayerExpLevel 查询玩家经验等级
func QueryPlayerExpLevel(ctx context.Context, playerId uint64) (int32, error) {
	expNum, err := QueryPlayerExpNum(ctx, playerId)
	if err != nil {
		return 0, err
	}

	// 计算等级
	expLevel, _, err := logicRole.CalcExpLevelFromExp(ctx, expNum, 0)
	if err != nil {
		return 0, err
	}

	return expLevel, nil
}

// 是否包含特殊字符
func containSpecial(s string) bool {
	special := "@#￥%&*<>《》?？/:；“\"'{}[]=+-`"
	for _, c := range special {
		if strings.ContainsRune(s, c) {
			return true
		}
	}
	return false
}