package logicPlayer

import (
	"context"
	logicItem "hallsrv/internal/logic/logic_item"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	hallPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/hall"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/rpc_biz/crpc_user"
	"git.keepfancy.xyz/back-end/frameworks/kit/forbid"
	"git.keepfancy.xyz/back-end/frameworks/kit/rpc/interceptor"
	"git.keepfancy.xyz/back-end/frameworks/lib/reflectext"
	"git.keepfancy.xyz/back-end/frameworks/lib/transform"
)

// 这里是客户端更新玩家逻辑的接口

type ModifyBase interface {
	// 检查修改玩家基础信息
	CheckModifyPlayerInfo(ctx context.Context, playerId uint64, param interface{}) (string, commonPB.ErrCode)
}


func ModifyPlayerInfo(ctx context.Context, playerId uint64, req *hallPB.ModifyPlayerInfoReq) error {
	if req == nil {
		return protox.CodeError(commonPB.ErrCode_ERR_BAD_PARAM)
	}

	// 解析出有效数据
	validParam, err := reflectext.GetStructValidMap(req)
	if err != nil {
		return protox.CodeError(commonPB.ErrCode_ERR_BAD_PARAM)
	}
	
	entry := logx.NewLogEntry(ctx)
	productId := interceptor.GetRPCOptions(ctx).ProductId

	var param string
	var modifyImpl ModifyBase = nil
	var errCode commonPB.ErrCode = commonPB.ErrCode_ERR_FAIL

	updateParam := make(map[string]string)
	
	for name, value := range validParam {
		switch name {
		case "nick_name":
			modifyImpl = new(NickName)
		case "avatar":
			modifyImpl = new(Avatar)
		case "frame":
			modifyImpl = new(Frame)
		default:
			modifyImpl = nil
		}

		if modifyImpl == nil {
			continue
		}

		param, errCode = modifyImpl.CheckModifyPlayerInfo(ctx, playerId, value)
		if errCode != commonPB.ErrCode_ERR_SUCCESS {
			break
		}
		updateParam[name] = param
	}

	if errCode != commonPB.ErrCode_ERR_SUCCESS {
		entry.Debugf("UpdatePlayerInfo req:%+v, code:%+v", req, errCode)
		return protox.CodeError(errCode)
	}

	if len(updateParam) == 0 {
		return nil
	}

	rpcRsp, err := crpc_user.RpcUpdatePlayerInfo(ctx, productId, playerId, updateParam)
	if err != nil {
		return err
	}

	entry.Debugf("UpdatePlayerInfo req:%+v, rpc rsp:%+v", req, rpcRsp)

	return nil
}

// 昵称
type NickName struct{
}

func (n *NickName) CheckModifyPlayerInfo(ctx context.Context, playerId uint64, param interface{}) (string, commonPB.ErrCode) {
	if n == nil {
		return "", commonPB.ErrCode_ERR_FAIL
	}

	nickName := param.(string)
	//  昵称最低不少于4个字符 不能超过16个字符
	nameLen := transform.GetStrLen(nickName)
	if nameLen < 4 || nameLen > 16 {
		return "", commonPB.ErrCode_ERR_NOT_ENOUGH
	}
	// 特殊字符
	if containSpecial(nickName) {
		return "", commonPB.ErrCode_ERR_BAD_PARAM
	}

	// 校验屏蔽字库
	existForbid := forbid.ExistForbid(nickName)
	if existForbid {
		return "", commonPB.ErrCode_ERR_FORBID_WORD
	}

	return nickName, commonPB.ErrCode_ERR_SUCCESS
}

// 头像
type Avatar struct{

}

func (a *Avatar) CheckModifyPlayerInfo(ctx context.Context, playerId uint64, param interface{}) (string, commonPB.ErrCode) {
	if a == nil {
		return "", commonPB.ErrCode_ERR_FAIL
	}

	avatarId := param.(int64)

	// 检查是否存在
	itemList, err := logicItem.PlayerQueryItemInfo(ctx, playerId, commonPB.ITEM_TYPE_IT_WEARABLE_AVATAR)
	if err != nil {
		return "", commonPB.ErrCode_ERR_OPERATION
	}

	flag := false
	for _, item := range itemList {
		if item.Item.ItemType == commonPB.ITEM_TYPE_IT_WEARABLE_AVATAR && item.Item.ItemId == avatarId {
			flag = true
			break
		}
	}

	if !flag {
		return "", commonPB.ErrCode_ERR_NOT_EXIST
	}

	return transform.Int642Str(avatarId), commonPB.ErrCode_ERR_SUCCESS
}

// 头像框
type Frame struct{
}

func (f *Frame) CheckModifyPlayerInfo(ctx context.Context, playerId uint64, param interface{}) (string, commonPB.ErrCode) {
	if f == nil {
		return "", commonPB.ErrCode_ERR_FAIL
	}

	frameId := param.(int64)

	// 检查是否存在头像框道具
	itemList, err := logicItem.PlayerQueryItemInfo(ctx, playerId, commonPB.ITEM_TYPE_IT_WEARABLE_HEAD_FRAME)
	if err != nil {
		return "", commonPB.ErrCode_ERR_OPERATION
	}

	flag := false
	for _, item := range itemList {
		if item.Item.ItemType == commonPB.ITEM_TYPE_IT_WEARABLE_HEAD_FRAME && item.Item.ItemId == frameId {
			flag = true
			break
		}
	}

	if !flag {
		return "", commonPB.ErrCode_ERR_NOT_EXIST
	}

	return transform.Int642Str(frameId), commonPB.ErrCode_ERR_SUCCESS
}