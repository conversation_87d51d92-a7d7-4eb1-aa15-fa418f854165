package logicItem

import (
	"context"
	test_init "hallsrv/internal/test"
	"testing"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/frameworks/kit/rpc/interceptor"
)

func TestPlayerQueryItemInfoByIdList(t *testing.T) {
	test_init.InitRedisConsul()
	itemList, err := PlayerQueryItemInfoByIdList(context.TODO(), 179, []int64{101, 102})
	t.Logf("itemList:%+v, err:%+v", itemList, err)
}

func TestPlayerQueryItemInfo(t *testing.T) {
	test_init.Init()
	var playerId uint64 = 443
	itemList, err := PlayerQueryItemInfo(context.TODO(), playerId, -1)
	t.Logf("itemList:%+v, err:%+v", itemList, err)
}

func TestOperatePlayerItem(t *testing.T) {
	test_init.Init()
	ctx := interceptor.NewRpcClientCtx(interceptor.WithProductId(1))
	itemList := []*commonPB.ItemBase{
		{
			ItemId:    3065001,
			ItemCount: 1,
		},
	}
	rewardInfo, err := OperatePlayerItem(ctx, 179, itemList, commonPB.ITEM_OPERATION_IO_ADD, commonPB.ITEM_SOURCE_TYPE_IST_DAILY_BONUS, commonPB.STORAGE_TYPE_ST_STORE, true)
	t.Logf("rewardInfo:%+v, err:%v", rewardInfo, err)
}
