package logicItem

import (
	"context"
	"fmt"
	"hallsrv/internal/logic/logic_player_state"
	modelItem "hallsrv/internal/model/model_item"
	modelPlayer "hallsrv/internal/model/model_player"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/frameworks/lib/algorithm/alg_bitmap"
)

// SendPlayerLevelUpReward 发送玩家升级奖励
func SendPlayerLevelUpReward(ctx context.Context, playerId uint64, expLevelMap map[int32][]*commonPB.ItemBase) error {
	entry := logx.NewLogEntry(ctx)
	if playerId <= 0 || len(expLevelMap) <= 0 {
		entry.Warnf("level up player:%d, expLevelMap is nil", playerId)
		return fmt.Errorf("level up player:%d, expLevelMap is nil", playerId)
	}

	// 查询等级领奖数据
	expLvRwInfo, err := logic_player_state.QueryPlayerStateBmStr(ctx, playerId, modelPlayer.GetPlayerStateBmExpLvRdsField())
	if err != nil {
		entry.Errorf("query player:%d exp level reward info error:%v", playerId, err)
		return err
	}

	curLvRwBm := expLvRwInfo

	// 组合发奖
	totalItemList := make([]*commonPB.ItemBase, 0)
	totalLevelList := make([]int32, 0)
	for levelId, itemList := range expLevelMap {
		if len(itemList) <= 0 {
			continue
		}
		// 判断是否领奖
		isRw := alg_bitmap.InBitmap(curLvRwBm, uint64(levelId))
		if isRw {
			entry.Warnf("level up player:%d, level:%d, itemList:%s, is_rw:%v", playerId, levelId, modelItem.ItemBase2JsonStr(itemList), isRw)
			continue
		}

		// 标记已领奖
		curLvRwBm, err = alg_bitmap.AddBit(curLvRwBm, uint64(levelId))
		if err != nil {
			entry.Errorf("level up player:%d, level:%d, itemList:%s, err:%v", playerId, levelId, modelItem.ItemBase2JsonStr(itemList), err)
			continue
		}
		totalItemList = append(totalItemList, itemList...)
		totalLevelList = append(totalLevelList, levelId)
	}

	// 有更新 更新数据库
	if curLvRwBm != expLvRwInfo && curLvRwBm != "" {
		err := logic_player_state.UpdatePlayerStateBmStr(ctx, playerId, modelPlayer.GetPlayerStateBmExpLvRdsField(), curLvRwBm)
		if err != nil {
			entry.Errorf("level up player:%d, levelList:%v, itemList:%s, err:%v", playerId, totalLevelList, modelItem.ItemBase2JsonStr(totalItemList), err)
			return err
		}
	}

	if len(totalItemList) <= 0 {
		entry.Debugf("player:%d level up:%+v, no reward", playerId, totalLevelList)
		return nil
	}

	// 发送奖励
	rewardInfo, err := OperatePlayerItem(ctx, playerId, totalItemList, commonPB.ITEM_OPERATION_IO_ADD, commonPB.ITEM_SOURCE_TYPE_IST_EXP_LEVEL_UP, commonPB.STORAGE_TYPE_ST_STORE, true)
	if err != nil || rewardInfo == nil {
		entry.Errorf("level up player:%d, levelList:%v, itemList:%s, rewardInfo:%v, err:%v", playerId, totalLevelList, modelItem.ItemBase2JsonStr(totalItemList), rewardInfo, err)
		return err
	}

	entry.Debugf("Send player:%d, levelList:%v, itemList:%s, rewardInfo:%v", playerId, totalLevelList, modelItem.ItemBase2JsonStr(totalItemList), rewardInfo)

	return nil
}
