package logicItem

import (
	"context"
	"fmt"

	logicRole "hallsrv/internal/logic/logic_role"
	modelItem "hallsrv/internal/model/model_item"
	rpcSpot "hallsrv/internal/repo/rpc_spot"
	rpcTrip "hallsrv/internal/repo/rpc_trip"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
)

// 这里处理玩家钓场中道具变化的逻辑
type PondItemChangeInterface interface {
	// 钓场道具变化逻辑
	PondItemAddChange(ctx context.Context, playerId uint64, rewardInfo *commonPB.Reward) (*commonPB.PondEventChangeInfo, error)

	// 钓场道具变化逻辑
	PondItemUseChange(ctx context.Context, playerId uint64, srcItemList []*modelItem.ItemOptParam) (*commonPB.PondEventChangeInfo, error)
}

// PlayerPondItemChange 玩家钓场道具变化(暂针对玩家体力值处理)
func PlayerPondItemChange(ctx context.Context, playerId uint64, changeType commonPB.ITEM_OPERATION, srcItemMap map[int32][]*modelItem.ItemOptParam, rewardInfo *commonPB.Reward) error {
	entry := logx.NewLogEntry(ctx)
	if playerId == 0 || len(srcItemMap) <= 0 || rewardInfo == nil {
		entry.Debugf("item change param empty")
		return fmt.Errorf("param error")
	}

	// 查询玩家是否在钓场中
	roomInfo, err := rpcTrip.RpcGetPlayerRoomInfo(ctx, playerId)
	if err != nil || roomInfo == nil || roomInfo.GetRoomId() == "" {
		entry.Debugf("get player:%d, room info:%s, ret:%+v", playerId, roomInfo.String(), err)
		return nil
	}

	eventList := make([]*commonPB.PondEventChangeInfo, 0)
	for itemType, srcItemList := range srcItemMap {
		if len(srcItemList) <= 0 {
			continue
		}

		var pondItemChange PondItemChangeInterface

		switch commonPB.ITEM_TYPE(itemType) {
		case commonPB.ITEM_TYPE_IT_CURRENCY_EXP:
			pondItemChange = new(AddExpItem)
		case commonPB.ITEM_TYPE_IT_PROP_FOOD:
			pondItemChange = new(UseFoodItem)
		default:
			pondItemChange = nil
		}

		var eventInfo *commonPB.PondEventChangeInfo
		if pondItemChange != nil {
			switch commonPB.ITEM_OPERATION(changeType) {
			case commonPB.ITEM_OPERATION_IO_ADD:
				eventInfo, err = pondItemChange.PondItemAddChange(ctx, playerId, rewardInfo)
			case commonPB.ITEM_OPERATION_IO_REDUCE:
				eventInfo, err = pondItemChange.PondItemUseChange(ctx, playerId, srcItemList)
			}

			if eventInfo != nil && err == nil {
				eventList = append(eventList, eventInfo)
			}
		}
	}

	// 发送给钓点服更新数据
	err = rpcSpot.PondEventChangeNtf(ctx, playerId, roomInfo, eventList)

	return err
}

// 增加经验 只需要实现增加的接口即可
type AddExpItem struct {
}

// 推送等级变化
func (a *AddExpItem) PondItemAddChange(ctx context.Context, playerId uint64, rewardInfo *commonPB.Reward) (*commonPB.PondEventChangeInfo, error) {
	changeInfo := &commonPB.PondEventChangeInfo{
		EventId: commonPB.POND_EVENT_CHANGE_TYPE_PECV_EX_LEVEL,
	}
	entry := logx.NewLogEntry(ctx)

	var beforeExp, afterExp int64
	// 玩家增加经验
	for _, itemInfo := range rewardInfo.GetItemList() {
		if itemInfo.GetItem().GetItemType() == commonPB.ITEM_TYPE_IT_CURRENCY_EXP && itemInfo.GetItemCount() > 0 {
			beforeExp = itemInfo.GetItemCount() - itemInfo.GetItemDeltaCount()
			afterExp = itemInfo.GetItemCount()
			break
		}
	}

	// 判断等级是否增加
	beforeLevel, afterLevel, err := logicRole.CalcExpLevelFromExp(ctx, beforeExp, afterExp)
	if err != nil {
		entry.Warnf("calc exp level error:%+v, beforeExp:%d, afterExp:%d", beforeExp, afterExp, err)
		return nil, err
	}

	// 等级变化才需要通知
	if afterLevel > beforeLevel {
		changeInfo.BeforeNum = int64(beforeLevel)
		changeInfo.AfterNum = int64(afterLevel)
	} else {
		changeInfo = nil
	}

	entry.Debugf("player:%d, add exp change info:%+v, rewardInfo:%s, before level:%d, after level:%d", playerId, changeInfo, rewardInfo.String(), beforeLevel, afterLevel)

	return changeInfo, nil
}

func (a *AddExpItem) PondItemUseChange(ctx context.Context, playerId uint64, srcItemList []*modelItem.ItemOptParam) (*commonPB.PondEventChangeInfo, error) {

	return nil, nil
}

// 使用食物 只需要实现使用的接口即可
type UseFoodItem struct {
}

func (u *UseFoodItem) PondItemAddChange(ctx context.Context, playerId uint64, rewardInfo *commonPB.Reward) (*commonPB.PondEventChangeInfo, error) {
	return nil, nil
}

func (u *UseFoodItem) PondItemUseChange(ctx context.Context, playerId uint64, srcItemList []*modelItem.ItemOptParam) (*commonPB.PondEventChangeInfo, error) {
	entry := logx.NewLogEntry(ctx)
	changeInfo := &commonPB.PondEventChangeInfo{
		EventId: commonPB.POND_EVENT_CHANGE_TYPE_PECV_ENERGY_CHANGE,
	}

	var addEnergy int64
	// 玩家增加经验
	for _, itemParam := range srcItemList {
		if commonPB.ITEM_TYPE(itemParam.ItemType) == commonPB.ITEM_TYPE_IT_PROP_FOOD {
			addEnergy += int64(logicRole.CalcItemFoodAddEnergy(ctx, itemParam.ItemId)) * itemParam.ItemNum
		}
	}

	if addEnergy > 0 {
		changeInfo.ChangeNum = int64(addEnergy)
	} else {
		changeInfo = nil
	}

	entry.Debugf("player:%d, use food change info:%+v", playerId, changeInfo)

	return changeInfo, nil
}
