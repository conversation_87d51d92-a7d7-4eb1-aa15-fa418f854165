package logic_ann

import (
	"context"
	"encoding/json"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"hallsrv/internal/dao/dao_ann"
)

// QueryPopupInfo  查询拍脸图信息
func QueryPopupInfo(ctx context.Context, channelId int32) ([]*commonPB.AnnPopupInfo, error) {
	entry := logx.NewLogEntry(ctx)
	// todo 加缓存
	popup, err := dao_ann.QueryAnnPopUpInfoRdb(channelId)
	if err != nil {
		entry.Errorf("QueryPopupInfo falied channel_id=%d err=%s", channelId, err)
		return nil, err
	}

	// 转换结构体
	results := make([]*commonPB.AnnPopupInfo, 0, len(popup))
	for _, record := range popup {
		// 解析JSON字段（带错误处理）
		var content commonPB.AnnContent
		if err = json.Unmarshal([]byte(record.Content), &content); err != nil {
			entry.Errorf("content parsing failed: %v", err)
			continue
		}

		var action commonPB.AnnAction
		if err = json.Unmarshal([]byte(record.Action), &action); err != nil {
			entry.Errorf("action parsing failed: %v", err)
			continue
		}

		var cond commonPB.AnnConditions
		if err = json.Unmarshal([]byte(record.Conditions), &cond); err != nil {
			entry.Errorf("conditions parsing failed: %v", err)
			continue
		}

		results = append(results, &commonPB.AnnPopupInfo{
			Id:            record.Id,
			Priority:      record.Priority,
			ChannelId:     record.ChannelId,
			AnnContent:    &content,
			PopStyle:      record.PopStyle, // 默认值
			AnnAction:     &action,
			AnnConditions: &cond,
			StartTime:     record.StartTime,
			EndTime:       record.EndTime,
		})
	}

	return results, nil
}
