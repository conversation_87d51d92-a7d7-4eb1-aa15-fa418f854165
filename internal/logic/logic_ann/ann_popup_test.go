package logic_ann

import (
	"context"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	test_init "hallsrv/internal/test"
	"reflect"
	"testing"
)

func TestQueryPopupInfo(t *testing.T) {
	test_init.InitSql()

	type args struct {
		ctx       context.Context
		channelId int32
	}
	tests := []struct {
		name    string
		args    args
		want    []*commonPB.AnnPopupInfo
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			args: args{
				ctx:       context.Background(),
				channelId: 1001,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := QueryPopupInfo(tt.args.ctx, tt.args.channelId)
			if (err != nil) != tt.wantErr {
				t.Errorf("QueryPopupInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.<PERSON><PERSON><PERSON>("QueryPopupInfo() got = %v, want %v", got, tt.want)
			}
		})
	}
}
