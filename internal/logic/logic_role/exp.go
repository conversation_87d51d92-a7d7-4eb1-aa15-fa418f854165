package logicRole

import (
	"context"
	"fmt"
	"sort"

	logicNotify "hallsrv/internal/logic/logic_notify"
	"hallsrv/internal/pubsub/publish"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/rpc_biz/crpc_user"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	"git.keepfancy.xyz/back-end/frameworks/kit/rpc/interceptor"
	"git.keepfancy.xyz/back-end/frameworks/lib/transform"
)

// CheckPlayerExpLevelChange 发送玩家等级变化信息
func CheckPlayerExpLevelChange(ctx context.Context, playerId uint64, rewardInfo *commonPB.Reward) (map[int32][]*commonPB.ItemBase, error) {
	entry := logx.NewLogEntry(ctx)
	if rewardInfo == nil {
		entry.Errorf("check player:%d exp level change: no reward info", playerId)
		return nil, fmt.Errorf("no reward info")
	}

	var beforeExp, afterExp int64
	for _, rewardItem := range rewardInfo.GetItemList() {
		if rewardItem.GetItem().GetItemType() == commonPB.ITEM_TYPE_IT_CURRENCY_EXP {
			beforeExp = rewardItem.GetItemCount() - rewardItem.GetItemDeltaCount()
			afterExp = rewardItem.GetItemCount()
		}
	}

	if afterExp <= 0 || afterExp <= beforeExp {
		entry.Debugf("check player:%d exp level change: no exp info, rewardInfo:%s", playerId, rewardInfo.String())
		return nil, nil
	}

	beforeLevel, afterLevel, err := CalcExpLevelFromExp(ctx, beforeExp, afterExp)
	if err != nil {
		entry.Errorf("CalcExpLevelFromExp err:%+v", err)
		return nil, fmt.Errorf("calc exp level info err:%+v", err)
	}

	// 0经验的时候 =1级
	if afterLevel > beforeLevel || beforeExp == 0 {
		// 更新玩家等级信息
		productId := interceptor.GetRPCOptions(ctx).ProductId
		updateParams := make(map[string]string)
		updateParams["lev"] = transform.Int642Str(int64(afterLevel))
		_, err := crpc_user.RpcUpdatePlayerInfo(ctx, productId, playerId, updateParams)
		if err != nil {
			entry.Warnf("role_lev update user fail:%+v", err)
		}
		publish.PublishLevChange(ctx, playerId, int64(afterLevel), int64(beforeLevel))
	}

	changeMap, err := CalcExpLevelChangeInfo(ctx, beforeExp, afterExp)
	if err != nil || len(changeMap) <= 0 {
		entry.Debugf("check player:%d exp change map:%+v, or ret:%+v, rewardInfo:%s", playerId, changeMap, err, rewardInfo.String())
		return nil, fmt.Errorf("calc exp level info err")
	}

	// 等级变化通知
	logicNotify.PlayerLevelChangeNotify(ctx, playerId, changeMap, afterExp)

	return changeMap, nil
}

// CalcExpLevelChangeInfo 计算等级变化 返回每个等级变化信息及奖励信息
func CalcExpLevelChangeInfo(ctx context.Context, beforeExp int64, afterExp int64) (map[int32][]*commonPB.ItemBase, error) {
	entry := logx.NewLogEntry(ctx)
	if beforeExp < 0 || afterExp <= 0 {
		entry.Errorf("calc exp level change info: before_exp:%d,after_exp:%d", beforeExp, afterExp)
		return nil, fmt.Errorf("before_exp or after_exp is invalid")
	}

	expLevelConf := cmodel.GetAllRoleLevel(consul_config.WithGrpcCtx(ctx))
	if expLevelConf == nil {
		entry.Errorf("calc exp level from exp: no role level config")
		return nil, fmt.Errorf("no role level config")
	}

	// 将expLevelConf的map结构按key从大到小排序
	expLevelList := make([]*cmodel.RoleLevel, 0, len(expLevelConf))
	for _, levelItem := range expLevelConf {
		expLevelList = append(expLevelList, levelItem)
	}

	sort.Slice(expLevelList, func(i, j int) bool {
		return expLevelList[i].Id < expLevelList[j].Id
	})

	var beforeLevel, afterLevel int32
	for _, expConfig := range expLevelList {
		if expConfig.ExpNum <= beforeExp {
			beforeLevel = int32(expConfig.Id)
		}

		if expConfig.ExpNum <= afterExp {
			afterLevel = int32(expConfig.Id)
		}
	}

	// 组合等级和奖励
	levelStageInfo := make(map[int32][]*commonPB.ItemBase, 0)
	for i := beforeLevel; i < afterLevel; i++ {
		rewardList := make([]*commonPB.ItemBase, 0)
		for _, rewardItem := range expLevelList[i].Reward {
			rewardList = append(rewardList, &commonPB.ItemBase{
				ItemId:    rewardItem.ItemId,
				ItemCount: rewardItem.ItemCount,
			})
		}
		levelStageInfo[int32(expLevelList[i].Id)] = rewardList
	}

	return levelStageInfo, nil
}

// CalcExpLevelFromExp 根据经验值计算等级
func CalcExpLevelFromExp(ctx context.Context, beforeExp int64, afterExp int64) (int32, int32, error) {
	entry := logx.NewLogEntry(ctx)
	expLevelConf := cmodel.GetAllRoleLevel(consul_config.WithGrpcCtx(ctx))
	if expLevelConf == nil {
		entry.Errorf("calc exp level from exp : no role level config")
		return 0, 0, fmt.Errorf("no role level config")
	}

	// 将expLevelConf的map结构按key从大到小排序
	expLevelList := make([]*cmodel.RoleLevel, 0, len(expLevelConf))
	for _, levelItem := range expLevelConf {
		expLevelList = append(expLevelList, levelItem)
	}

	sort.Slice(expLevelList, func(i, j int) bool {
		return expLevelList[i].Id < expLevelList[j].Id
	})

	var beforeLevel, afterLevel int32
	for _, expConfig := range expLevelList {
		if expConfig.ExpNum <= beforeExp {
			beforeLevel = int32(expConfig.Id)
		}
		if expConfig.ExpNum <= afterExp {
			afterLevel = int32(expConfig.Id)
		}
	}

	return beforeLevel, afterLevel, nil
}

// GetExpLevelRewardFromExp 获取指定经验的等级奖励信息
func GetExpLevelRewardFromExp(ctx context.Context, exp int64) (map[int32][]*commonPB.ItemBase, error) {
	entry := logx.NewLogEntry(ctx)
	if exp < 0 {
		entry.Errorf("get exp level reward from exp : exp:%d", exp)
		return nil, fmt.Errorf("exp is invalid")
	}

	expLevelConf := cmodel.GetAllRoleLevel(consul_config.WithGrpcCtx(ctx))
	if expLevelConf == nil {
		entry.Errorf("get exp level reward from exp : no role level config")
		return nil, fmt.Errorf("no role level config")
	}

	// 将expLevelConf的map结构按key从大到小排序
	expLevelList := make([]*cmodel.RoleLevel, 0, len(expLevelConf))
	for _, levelItem := range expLevelConf {
		expLevelList = append(expLevelList, levelItem)
	}

	sort.Slice(expLevelList, func(i, j int) bool {
		return expLevelList[i].Id > expLevelList[j].Id
	})

	levelRewardMap := make(map[int32][]*commonPB.ItemBase, 0)

	for _, expConfig := range expLevelList {
		if expConfig.ExpNum <= exp {
			levelRewardList := make([]*commonPB.ItemBase, 0)
			for _, rewardItem := range expConfig.Reward {
				levelRewardList = append(levelRewardList, &commonPB.ItemBase{
					ItemId:    rewardItem.ItemId,
					ItemCount: rewardItem.ItemCount,
				})
			}
			levelRewardMap[int32(expConfig.Id)] = levelRewardList
			break
		}
	}

	return levelRewardMap, nil
}
