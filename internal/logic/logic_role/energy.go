package logicRole

import (
	"context"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
)

// CalcItemFoodAddEnergy 计算食物增加的体力
func CalcItemFoodAddEnergy(ctx context.Context, itemId int64) int32 {
	entry := logx.NewLogEntry(ctx)
	itemFoodConf := cmodel.GetItemFood(int64(itemId), consulconfig.WithGrpcCtx(ctx))
	if itemFoodConf == nil {
		entry.Errorf("get food item:%d config error", itemId)
		return 0
	}

	return itemFoodConf.AddEnergy
}
