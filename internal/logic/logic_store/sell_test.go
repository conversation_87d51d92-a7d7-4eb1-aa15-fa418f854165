package logicStore

import (
	test_init "hallsrv/internal/test"
	"testing"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
)

func TestSell(t *testing.T) {
	test_init.Init()
	var playerId uint64 = 9000079

	list := []*commonPB.ItemBase{
		{
			ItemId:     3033001,
			InstanceId: "",
			ItemCount:  1,
		},
	}
	ctx := test_init.NewCtxWithPlayerId(playerId)
	cost, err := SellItem(ctx, playerId, commonPB.STORAGE_TYPE_ST_STORE, list)
	if err != nil {
		t.Fatalf("err:%+v", err)
	}
	t.Fatalf("cost:%+v", cost)
}
