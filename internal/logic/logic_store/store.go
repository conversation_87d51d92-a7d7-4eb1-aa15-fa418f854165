package logicStore

import (
	"context"
	daoGoods "hallsrv/internal/dao/dao_goods"
	daoRecharge "hallsrv/internal/dao/dao_recharge"
	"hallsrv/internal/logic/logic_bag"
	logicItem "hallsrv/internal/logic/logic_item"
	"hallsrv/internal/logic/logic_player_state"
	modelPlayer "hallsrv/internal/model/model_player"
	modelStore "hallsrv/internal/model/model_store"

	rpcUser "hallsrv/internal/repo/rpc_user"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	"git.keepfancy.xyz/back-end/frameworks/kit/rpc/interceptor"
	"git.keepfancy.xyz/back-end/frameworks/lib/algorithm/alg_bitmap"
	"git.keepfancy.xyz/back-end/frameworks/lib/transform"
)

// GetGoodsBuyInfo 处理商城信息
func GetGoodsBuyInfo(ctx context.Context, playerId uint64, goodsList []int64) ([]*commonPB.GoodsBuyInfo, error) {
	entry := logx.NewLogEntry(ctx)

	// 商品购买限制类型
	goodLimitTypeMap := make(map[int64]int32, len(goodsList))

	// 根据配置校验是否有限制类型
	goodsConf := cmodel.GetAllGoodsBasic(consulconfig.WithGrpcCtx(ctx))

	hasPermanentGoods := false
	for _, goodsId := range goodsList {
		if _, ok := goodsConf[goodsId]; !ok {
			entry.Warnf("player:%d get goods buy info, goods:%d not exist", playerId, goodsId)
			continue
		}

		if goodsConf[goodsId].LimitType == int32(commonPB.BUY_LIMIT_TYPE_BLT_NONE) {
			entry.Warnf("player:%d get goods buy info, goods:%d limit type is none", playerId, goodsId)
			continue
		}

		goodLimitTypeMap[goodsId] = goodsConf[goodsId].LimitType

		if goodsConf[goodsId].LimitType == int32(commonPB.BUY_LIMIT_TYPE_BLT_FOREVER) {
			hasPermanentGoods = true
		}
	}

	// 获取玩家购买信息
	playerBuyInfoMap, err := daoGoods.QueryPlayerAllGoodsBuyInfo(ctx, playerId)
	if err != nil {
		entry.Errorf("get player buy info error, player:%d", playerId)
		return nil, err
	}

	// 存在永久限制商品
	var playerPermanentGoods string
	if hasPermanentGoods {
		// 查询永久购买礼包信息
		playerPermanentGoods, err = logic_player_state.QueryPlayerStateBmStr(ctx, playerId, modelPlayer.GetPlayerStateBmGoodsRdsField())
		if err != nil {
			entry.Errorf("get player buy info error, player:%d", playerId)
			return nil, err
		}
	}

	goodsBuyInfoList := make([]*commonPB.GoodsBuyInfo, 0, len(goodsList))

	// 根据购买信息计算是否被限制
	for goodsId, limitType := range goodLimitTypeMap {
		// 永久限制商品
		if limitType == int32(commonPB.BUY_LIMIT_TYPE_BLT_FOREVER) {
			if alg_bitmap.InBitmap(playerPermanentGoods, uint64(goodsId)) {
				entry.Infof("player:%d get goods buy info, goods:%d is permanent", playerId, goodsId)
				// 添加到购买信息中
				goodsBuyProto := &commonPB.GoodsBuyInfo{
					GoodsId:     goodsId,
					BuyTimes:    1,
					LastBuyTime: 0,
				}
				goodsBuyInfoList = append(goodsBuyInfoList, goodsBuyProto)
			}
			continue
		}

		if _, ok := playerBuyInfoMap[goodsId]; !ok {
			entry.Debugf("player:%d get goods buy info, goods:%d not exist", playerId, goodsId)
			continue
		}

		// 普通限制类型
		buyInfo := playerBuyInfoMap[goodsId]
		buyInfo.CalTimes(limitType)
		goodsBuyInfoList = append(goodsBuyInfoList, buyInfo.ToProto())
	}

	return goodsBuyInfoList, nil
}

// PlayerBuyStoreGoods 玩家购买商品
func PlayerBuyStoreGoods(ctx context.Context, playerId uint64, storeStyle commonPB.STORE_SHOW_STYLE, storeBuyId int64, buyCount int32) (*commonPB.Reward, error) {
	entry := logx.NewLogEntry(ctx)
	if storeBuyId <= 0 || buyCount < 1 {
		entry.Warnf("player:%d buy store goods, param is error", playerId)
		return nil, protox.CodeError(commonPB.ErrCode_ERR_BAD_PARAM, "buyId or buyCount")
	}

	goodsInfo, err := modelStore.NewGoodsInfo(ctx, storeBuyId, storeStyle)
	if goodsInfo == nil || goodsInfo.GoodsConf == nil || err != nil {
		entry.Errorf("player:%d buy store id:%d err:%+v", playerId, storeBuyId, err)
		return nil, err
	}

	// 添加道具存储的位置
	storeType := commonPB.STORAGE_TYPE_ST_STORE
	if storeStyle == commonPB.STORE_SHOW_STYLE_STRT_ROOM {
		storeType = commonPB.STORAGE_TYPE_ST_BAG
	}

	// 计算消耗道具和数量
	costItemId := goodsInfo.CostItem
	costCount := goodsInfo.CostCount * int64(buyCount)

	// 消耗道具信息
	costItemBase := []*commonPB.ItemBase{
		{
			ItemId:    costItemId,
			ItemCount: costCount,
		},
	}

	// 添加数量
	addItemCount := goodsInfo.GoodsConf.ItemCount * int64(buyCount)
	// 添加奖励信息
	addItemBase := []*commonPB.ItemBase{
		{
			ItemId:    goodsInfo.GoodsConf.ItemId,
			ItemCount: addItemCount,
		},
	}

	addReward, err := ExchangeItem(ctx, playerId, commonPB.ITEM_SOURCE_TYPE_IST_STORE_BUY, commonPB.STORAGE_TYPE_ST_STORE, storeType, costItemBase, addItemBase)
	if err != nil {
		entry.Errorf("player:%d buy store goods, exchange item error:%+v", playerId, err)
		return nil, err
	}

	entry.Infof("player:%d buy store goods, storeBuyId:%d, buy count:%d, costItem:%d, costNum:%d, addCount:%d", playerId, storeBuyId, buyCount, costItemId, costCount, addItemCount)

	return addReward, nil
}

// PlayerBuyMultiStoreGoods 玩家批量购买商品
func PlayerBuyMultiStoreGoods(ctx context.Context, playerId uint64, storeStyle commonPB.STORE_SHOW_STYLE, storeBuyList []*commonPB.ItemBase) (*commonPB.Reward, error) {
	entry := logx.NewLogEntry(ctx)
	if len(storeBuyList) <= 0 {
		entry.Warnf("player:%d buy multi store goods, param is error", playerId)
		return nil, protox.CodeError(commonPB.ErrCode_ERR_BAD_PARAM, "buyList empty")
	}
	// 消耗道具信息
	costItemBase := make([]*commonPB.ItemBase, 0)
	// 奖励信息
	addItemBase := make([]*commonPB.ItemBase, 0)

	// 组合购买
	for _, v := range storeBuyList {

		// 获取商品详细信息
		goodsInfo, err := modelStore.NewGoodsInfo(ctx, v.ItemId, storeStyle)
		if goodsInfo == nil || goodsInfo.GoodsConf == nil || err != nil {
			entry.Errorf("player:%d buy store id:%d err:%+v", playerId, v.ItemId, err)
			return nil, err
		}

		// 商品配置信息 只处理非限制商品
		if goodsInfo.GoodsConf.LimitType != int32(commonPB.BUY_LIMIT_TYPE_BLT_NONE) {
			entry.Warnf("player:%d buy multi store goods, goods:%d in limit", playerId, v.ItemId)
			return nil, protox.CodeError(commonPB.ErrCode_ERR_CONF_ERROR, "multi goods cont limit")
		}

		// 计算消耗道具和数量
		costItemId := goodsInfo.CostItem
		costCount := goodsInfo.CostCount * v.ItemCount

		// 消耗道具信息
		costItemBase = append(costItemBase, &commonPB.ItemBase{
			ItemId:    costItemId,
			ItemCount: costCount,
		})

		// 计算奖励数量
		addItemCount := goodsInfo.GoodsConf.ItemCount * v.ItemCount
		// 奖励信息
		addItemBase = append(addItemBase, &commonPB.ItemBase{
			ItemId:    goodsInfo.GoodsConf.ItemId,
			ItemCount: addItemCount,
		})
	}

	// 房间商品 判断是否有格子
	if storeStyle == commonPB.STORE_SHOW_STYLE_STRT_ROOM {
		err := logic_bag.CheckPlayerBagSpace(ctx, playerId, commonPB.ITEM_OPERATION_IO_ADD, addItemBase)
		if err != nil {
			return nil, protox.CodeError(commonPB.ErrCode_ERR_HALL_BAG_FULL, "store room bag space is not enough")
		}
	}

	// 添加道具存储的位置
	storeType := commonPB.STORAGE_TYPE_ST_STORE
	if storeStyle == commonPB.STORE_SHOW_STYLE_STRT_ROOM {
		storeType = commonPB.STORAGE_TYPE_ST_BAG
	}

	addReward, err := ExchangeItem(ctx, playerId, commonPB.ITEM_SOURCE_TYPE_IST_STORE_BUY, commonPB.STORAGE_TYPE_ST_STORE, storeType, costItemBase, addItemBase)
	if err != nil {
		entry.Errorf("player:%d buy store goods, exchange item error:%+v", playerId, err)
		return nil, err
	}

	entry.Infof("player:%d buy multi store goods, cost item:%v, add reward:%v", playerId, costItemBase, addReward)

	return addReward, nil
}

// TempCashBuy 临时现金购买商品
func TempCashBuy(ctx context.Context, playerId uint64, buyId int64) (*commonPB.Reward, error) {
	entry := logx.NewLogEntry(ctx)
	productId := interceptor.GetRPCOptions(ctx).ProductId

	// 查询配置
	storeConf := cmodel.GetPurchaseList(buyId, consul_config.WithGrpcCtx(ctx))
	if storeConf == nil {
		entry.Warnf("player:%d temp cash buy, conf not found", playerId)
		return nil, protox.CodeError(commonPB.ErrCode_ERR_CONF_ERROR, "temp cash buy conf not found")
	}

	// 判断是否限制充值
	realNameConf := cmodel.GetRealNameAuth(consul_config.WithGrpcCtx(ctx))
	if realNameConf != nil && realNameConf.Enable {
		// 查询玩家年龄类型
		ageType, err := rpcUser.RpcQueryPlayerAgeInfo(ctx, productId, playerId)
		if ageType == commonPB.USER_AGE_UA_UNKNOWN || err != nil {
			entry.Errorf("query player:%d age info failed:%+v", playerId, err)
			return nil, protox.CodeError(commonPB.ErrCode_ERR_LOGIN_ANTI_ADDICTION, "query player age info failed")
		}

		// 判断是否不可充值
		if transform.Int32SliceContain(realNameConf.RechargeUnallowedAgeType, int32(ageType)) {
			entry.Warnf("player:%d temp cash buy, ageType:%d not allow recharge", playerId, ageType)
			return nil, protox.CodeError(commonPB.ErrCode_ERR_ORDER_UNBALLOWED_RECHARGE_AGE, "age type not allow recharge")
		}

		// 判断是否限制充值
		if transform.Int32SliceContain(realNameConf.RechargeLimitAgeType, int32(ageType)) {

			// 查询充值限制配置
			var rechargeLimitConf *cmodel.RechargeLimit
			allLimitConf := cmodel.GetAllRechargeLimit(consul_config.WithGrpcCtx(ctx))
			for _, limitConf := range allLimitConf {
				if limitConf.AgeType == int32(ageType) {
					rechargeLimitConf = limitConf
					break
				}
			}

			// 判断是否有单笔充值限制
			if transform.Int32SliceContain(realNameConf.RechargeLimitAgeType, int32(ageType)) {
				// 单笔充值
				if rechargeLimitConf != nil && storeConf.DollarCent > rechargeLimitConf.SingleLimit {
					entry.Warnf("player:%d temp cash buy, ageType:%d recharge limit", playerId, ageType)
					return nil, protox.CodeError(commonPB.ErrCode_ERR_ORDER_SINGLE_LIMIT_AGE, "recharge limit")
				}
			}

			// 查询玩家月充值金额
			totalAmt, err := daoRecharge.QueryPlayerMonthRechargeRds(ctx, playerId)
			if err != nil {
				entry.Errorf("query player:%d month recharge failed:%+v", playerId, err)
				return nil, protox.CodeError(commonPB.ErrCode_ERR_OPERATION, "query player month recharge failed")
			}

			// 判断是否超过月限额
			if rechargeLimitConf != nil && totalAmt+storeConf.DollarCent > rechargeLimitConf.MonthLimit {
				entry.Warnf("player:%d temp cash buy, ageType:%d month recharge limit", playerId, ageType)
				return nil, protox.CodeError(commonPB.ErrCode_ERR_ORDER_MONTH_LIMIT_AGE, "month recharge limit")
			}
		}
	}

	// 更新玩家月购买金额
	err := daoRecharge.AddPlayerMonthRechargeRds(ctx, playerId, storeConf.DollarCent)
	if err != nil {
		entry.Errorf("update player:%d month recharge failed:%+v", playerId, err)
		return nil, protox.CodeError(commonPB.ErrCode_ERR_OPERATION, "update player month recharge failed")
	}

	// 购买流程
	goodsConf := cmodel.GetGoodsBasic(storeConf.GoodsId, consul_config.WithGrpcCtx(ctx))
	if goodsConf == nil {
		entry.Warnf("player:%d temp cash buy, goods conf not found", playerId)
		return nil, protox.CodeError(commonPB.ErrCode_ERR_CONF_ERROR, "temp cash buy goods conf not found")
	}

	addItem := &commonPB.ItemBase{
		ItemId:    goodsConf.ItemId,
		ItemCount: goodsConf.ItemCount,
	}

	rewardList := []*commonPB.ItemBase{addItem}

	// TODO 假购买 直接添加道具
	addReward, err := logicItem.OperatePlayerItem(ctx, playerId, rewardList, commonPB.ITEM_OPERATION_IO_ADD, commonPB.ITEM_SOURCE_TYPE_IST_STORE_BUY, commonPB.STORAGE_TYPE_ST_STORE, true)
	if err != nil || addReward == nil {
		entry.Errorf("player:%d cash buy send reward, rewardList:%+v is error:%+v", playerId, rewardList, err)
		return nil, protox.CodeError(commonPB.ErrCode_ERR_OPERATION, "add reward error")
	}

	return addReward, nil
}
