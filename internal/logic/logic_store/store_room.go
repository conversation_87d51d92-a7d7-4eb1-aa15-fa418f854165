package logicStore

import (
	"context"
	"hallsrv/internal/logic/logic_bag"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
)

// CheckStoreRoomBagSpace 房间购买商品 校验背包空间
func CheckStoreRoomBagSpace(ctx context.Context, playerId uint64, goodsConf *cmodel.GoodsBasic, buyCount int32) bool {
	entry := logx.NewLogEntry(ctx)

	if goodsConf == nil{
		entry.Errorf("player:%d goods info nil", playerId)
		return false
	}

	addItem := goodsConf.ItemId
	addCount := goodsConf.ItemCount * int64(buyCount)
	itemBaseList := []*commonPB.ItemBase{{ItemId: addItem, ItemCount: addCount}}
	
	err := logic_bag.CheckPlayerBagSpace(ctx, playerId, commonPB.ITEM_OPERATION_IO_ADD, itemBaseList)
	if err != nil {
		entry.Warnf("player:%d buy goodsInfo:%+v count:%d not space err:%+v", playerId, goodsConf, buyCount, err)
		return false
	}

	return true
}