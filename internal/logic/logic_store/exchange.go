package logicStore

import (
	"context"
	logicItem "hallsrv/internal/logic/logic_item"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
)

// ExchangeItem 兑换道具逻辑
func ExchangeItem(ctx context.Context, playerId uint64, srcType commonPB.ITEM_SOURCE_TYPE, costStore commonPB.STORAGE_TYPE, addStore commonPB.STORAGE_TYPE, costList []*commonPB.ItemBase, rewardList []*commonPB.ItemBase) (*commonPB.Reward, error) {
	entry := logx.NewLogEntry(ctx)

	if len(costList) <= 0 || len(rewardList) <= 0 {
		entry.Errorf("player:%d exchange item, srcType:%d, costStore:%d, addStore:%d, costItem:%+v, rewardItem:%+v error", playerId, costStore, addStore, srcType, costList, rewardList)
		return nil, protox.CodeError(commonPB.ErrCode_ERR_BAD_PARAM, "cost or reward is error") 
	}
	
	// 扣除道具
	rewordInfo, err := logicItem.OperatePlayerItem(ctx, playerId, costList, commonPB.ITEM_OPERATION_IO_REDUCE, srcType, costStore, true)

	if err != nil || rewordInfo == nil {
		entry.Errorf("player:%d exchange item, reduce srcType:%d, costStore:%d, addStore:%d, costItem:%+v, rewardItem:%+v is error:%+v", playerId, srcType, costStore, addStore, costList, rewardList, err)
		return nil, protox.CodeError(commonPB.ErrCode_ERR_HALL_ITEM_NOT_ENOUGH, "reduce item") 
	}

	// 添加道具
	addReward, err := logicItem.OperatePlayerItem(ctx, playerId, rewardList, commonPB.ITEM_OPERATION_IO_ADD, srcType, addStore, true)
	if err != nil || addReward == nil {
		entry.Errorf("player:%d exchange item, send reward, srcType:%d, costStore:%d, addStore:%d, costItem:%+v, rewardItem:%+v is error:%+v", playerId, srcType, costStore, addStore, costList, rewardList, err)
		return nil, protox.CodeError(commonPB.ErrCode_ERR_OPERATION, "add reward")
	}

	// TODO: 这里要写流水

	entry.Debugf("player:%d exchange item, srcType:%d, costStore:%d, addStore:%d, costItem:%+v, rewardItem:%+v is success", playerId, srcType, costStore, addStore, costList, rewardList)
	
	return addReward, nil
}