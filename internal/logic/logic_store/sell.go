package logicStore

import (
	"context"
	"fmt"
	logicItem "hallsrv/internal/logic/logic_item"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/item_kit"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
)

// 仅支持带instanceId
// SellItem 出售道具
func SellItem(ctx context.Context, playerId uint64, bagType commonPB.STORAGE_TYPE, list []*commonPB.ItemBase) (*commonPB.Reward, error) {
	entry := logx.NewLogEntry(ctx)
	if len(list) == 0 {
		return nil, protox.CodeError(commonPB.ErrCode_ERR_BAD_PARAM, "sell item list empty")
	}

	// 抽取所有ids
	ids := make([]int64, 0)
	for _, item := range list {
		ids = append(ids, item.ItemId)
	}
	// TODO 检查类型
	itemList, err := logicItem.PlayerQueryItemInfoByIdList(ctx, playerId, ids)
	if err != nil {
		entry.Errorf("query player:%d, itemIds:%v, err:%+v", playerId, ids, err)
		return nil, err
	}

	sellItems, err := item_kit.ExtractItem(ctx, itemList, list)
	if err != nil {
		return nil, err
	}

	itemCfg := cmodel.GetAllItem(consul_config.WithGrpcCtx(ctx))
	if itemCfg == nil {
		return nil, protox.CodeError(commonPB.ErrCode_ERR_CONF_ERROR, "item config not found")
	}
	// 检查出售道具类型

	// 检查道具是否存在
	rewardItems := make([]*commonPB.ItemBase, 0)
	for _, one := range sellItems {
		currAbility := item_kit.GetCurrDurability(ctx, one.GetItem())
		maxAbility := item_kit.GetMaxDurability(ctx, one.GetItem())
		if maxAbility == 0 {
			return nil, protox.CodeError(commonPB.ErrCode_ERR_BAD_PARAM, fmt.Sprintf("item durability max zero itemId:%d instanceId:%+v", one.GetItem().GetItemId(), one.GetItem().GetInstanceId()))
		}
		percent := currAbility * 10000 / maxAbility
		oneItemCfg := itemCfg[one.Item.ItemId]
		if oneItemCfg == nil {
			entry.Errorf("itemCfg not found, itemId: %d", one.Item.ItemId)
			return nil, protox.CodeError(commonPB.ErrCode_ERR_CONF_ERROR, fmt.Sprintf("item config not found:%d", one.Item.ItemId))
		}
		newPrice := oneItemCfg.Price * int64(percent) / 10000
		// 出售耐久度=0的道具，价格为1
		if newPrice <= 0 {
			newPrice = 1
		}
		reward := &commonPB.ItemBase{
			ItemId:    int64(commonPB.ITEM_TYPE_IT_CURRENCY_COIN),
			ItemCount: newPrice,
		}
		rewardItems = append(rewardItems, reward)
	}

	reduceList := make([]*commonPB.OriginLoot, 0)
	for _, one := range sellItems {
		reduceList = append(reduceList, &commonPB.OriginLoot{
			Item:  one.Item,
			Value: 1,
		})
	}

	// TODO: SOURCE
	// 扣除道具
	var reward *commonPB.Reward
	if len(reduceList) > 0 {
		_, err = logicItem.OperateItemByInstance(ctx, playerId, reduceList, commonPB.ITEM_OPERATION_IO_REDUCE, bagType, commonPB.ITEM_SOURCE_TYPE_IST_ITEM_SELL)
		if err != nil {
			return nil, err
		}
	}

	if len(rewardItems) > 0 {
		// 返还价值
		reward, err = logicItem.OperatePlayerItem(ctx, playerId, rewardItems, commonPB.ITEM_OPERATION_IO_ADD, commonPB.ITEM_SOURCE_TYPE_IST_ITEM_SELL, bagType, false)
		if err != nil {
			return nil, err
		}
	}

	entry.Infof("sell item success, playerId:%d, bagType:%d, list:%+v, reward:%+v", playerId, bagType, sellItems, reward)

	// TODO record

	return reward, nil
}
