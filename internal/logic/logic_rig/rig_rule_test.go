package logicRig

import (
	"hallsrv/internal/config"
	test_init "hallsrv/internal/test"
	"strings"
	"testing"
)

func TestGetRigRuleId(t *testing.T) {
	test_init.InitRedisConsul()
	config.InitConfig()
	
	LoadRigRuleCache()

	ok := strings.HasPrefix("2|1|1|2", "1|1")
	
	ruleId := GetRigRuleId([]int32{4,1,1,0,0})
	nextArr := GetPrefixMatchTypeList([]int32{2,1})
	
	t.Logf("ruleId:%d, %+v, %+v", ruleId, ok, nextArr)
}