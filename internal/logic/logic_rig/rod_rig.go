package logicRig

import (
	"context"
	"errors"
	"fmt"
	"hallsrv/internal/config"
	daoRodRig "hallsrv/internal/dao/dao_rod_rig"
	modelRodRig "hallsrv/internal/model/model_rod_rig"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
	"git.keepfancy.xyz/back-end/frameworks/kit/dlm"
	"git.keepfancy.xyz/back-end/frameworks/kit/forbid"
)

func fmtRodRigLock(playerId uint64) string {
	return fmt.Sprintf(config.RDS_KEY_ROD_RIG_LOCK, playerId)
}

func QueryPlayerRodRigInfo(ctx context.Context, playerId uint64) ([]*modelRodRig.TRodRigInfo, error) {
	entry := logx.NewLogEntry(ctx)
	// 查询玩家钓组信息
	rigArr, err := daoRodRig.QueryPlayerRodRigRds(ctx, playerId)

	// 如果redis有默认数据直接返回
	if errors.Is(err, redisx.Empty) {
		entry.Debugf("query player:%d rod rig info rds empty", playerId)
		return nil, nil
	}

	if err != nil {
		entry.Errorf("query player:%d rod rig info rds err:%v", playerId, err)
		return nil, err
	}

	if len(rigArr) > 0 {
		return rigArr, nil
	}

	// 查询数据库
	rigArr, err = daoRodRig.QueryPlayerRodRigInfoRdb(playerId)
	if err != nil {
		entry.Errorf("query player:%d rod rig info rdb err:%v", playerId, err)
		return nil, err
	}

	if len(rigArr) <= 0 {
		// 防空数据
		redisx.GetGeneralCli().HSetNil(ctx, daoRodRig.FmtRodRigInfoRdsKey(playerId))
	} else {
		// 缓存数据
		daoRodRig.CachePlayerRodRigInfoRds(ctx, playerId, rigArr)
	}

	return rigArr, nil
}

func UpdatePlayerRodRigInfo(ctx context.Context, playerId uint64, rodRigInfo *modelRodRig.TRodRigInfo) commonPB.ErrCode {
	entry := logx.NewLogEntry(ctx)
	if rodRigInfo == nil {
		entry.Errorf("update player:%d rod rig info nil", playerId)
		return commonPB.ErrCode_ERR_BAD_PARAM
	}

	// 校验屏蔽字库
	existForbid := forbid.ExistForbid(rodRigInfo.Name)
	if existForbid {
		entry.Warnf("update player:%d rod rig info:%+v has forbid", playerId, rodRigInfo)
		return commonPB.ErrCode_ERR_FORBID_WORD
	}

	// 加锁
	unlock := dlm.LockKey(fmtRodRigLock(playerId))
	defer unlock()

	// 校验是否符合规则
	subTypeList := rodRigInfo.GetSubItemList(ctx)
	ruleID := GetRigRuleId(subTypeList)

	if ruleID <= 0 {
		entry.Errorf("update player:%d rod rig info:%+v type list:%+v err", playerId, rodRigInfo, subTypeList)
		return commonPB.ErrCode_ERR_HALL_RIG_RULE_WRONG
	}

	// 更新数据库
	err := daoRodRig.UpdatePlayerRodRigInfoRdb(playerId, rodRigInfo)
	if err != nil {
		entry.Errorf("update player:%d rod rig info:%+v rdb err:%v", playerId, rodRigInfo, err)
		return commonPB.ErrCode_ERR_OPERATION
	}

	// 删除缓存
	daoRodRig.ClearPlayerRodRigRds(ctx, playerId)

	return commonPB.ErrCode_ERR_SUCCESS
}

func DeletePlayerRodRigInfo(ctx context.Context, playerId uint64, rigId int32) error {
	// 加锁
	entry := logx.NewLogEntry(ctx)
	unlock := dlm.LockKey(fmtRodRigLock(playerId))
	defer unlock()

	// 删除数据库
	err := daoRodRig.DelPlayerRodRigInfoRdb(playerId, rigId)
	if err != nil {
		entry.Errorf("delete player:%d rod rig info:%d rdb err:%v", playerId, rigId, err)
		return err
	}

	// 清空缓存
	daoRodRig.ClearPlayerRodRigRds(ctx, playerId)

	return nil
}
