package logicNotify

import (
	"context"
	"hallsrv/internal/model/model_bag"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	hallPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/hall"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/sender"
)

// PlayerRewardInfoNotify 通知玩家刷新道具
func PlayerRewardInfoNotify(ctx context.Context, playerId uint64, storeType commonPB.STORAGE_TYPE, rewardInfo *commonPB.Reward) error {
	entry := logx.NewLogEntry(ctx)
	ntfBody := &hallPB.UpdateItemInfoNtf{
		RewardInfo: rewardInfo,
		Storage:    storeType,
	}

	err := sender.NotifyToPlayer(ctx, playerId, commonPB.MsgID_CMD_UPDATE_ITEM_NTF, ntfBody)
	if err != nil {
		entry.Errorf("notify player %d update item info error: %v", playerId, err)
		return err
	}

	entry.Debugf("notify player %d update item info success, btf:%s", playerId, ntfBody.String())

	return nil
}

// PlayerLevelChangeNotify 玩家等级变化通知
func PlayerLevelChangeNotify(ctx context.Context, playerId uint64, levelMap map[int32][]*commonPB.ItemBase, curExp int64) error {
	entry := logx.NewLogEntry(ctx)
	if len(levelMap) <= 0 || curExp < 0 {
		return nil
	}

	changeList := make([]*commonPB.ExpLevelChangeInfo, 0, len(levelMap))
	for level, rewardList := range levelMap {
		changeList = append(changeList, &commonPB.ExpLevelChangeInfo{
			ExpLevel: level,
			ItemList: rewardList,
		})
	}

	// 发送等级变化信息
	ntfBody := &hallPB.ExpLevelChangeNtf{
		CurExp:          curExp,
		LevelChangeInfo: changeList,
	}

	err := sender.NotifyToPlayer(ctx, playerId, commonPB.MsgID_CMD_EXP_LEVEL_CHANGE_NTF, ntfBody)

	if err != nil {
		entry.Errorf("notify player %d update level error: %v", playerId, err)
		return err
	}

	entry.Debugf("notify player %d update level success, levelMap:%v, ntf:%s", playerId, levelMap, ntfBody.String())

	return nil
}

// PlayerRodRigNotify 玩家钓竿杆组信息更新信息变更通知
func PlayerRodRigNotify(ctx context.Context, playerId uint64, rods ...*model_bag.TTripRodGroup) error {
	entry := logx.NewLogEntry(ctx)
	if len(rods) == 0 {
		return nil
	}

	changeList := make([]*commonPB.RodBagInfo, 0)
	for _, rod := range rods {
		if rod != nil && rod.Id > 0 {
			changeList = append(changeList, rod.ToProto(ctx))
		}
	}

	ntfBody := &hallPB.UpdateRodRigNtf{
		RodInfo: changeList,
	}
	err := sender.NotifyToPlayer(ctx, playerId, commonPB.MsgID_CMD_UPDATE_ROD_RIG_NTF, ntfBody)
	if err != nil {
		entry.Errorf("notify player[%d] rod:%+v err:%+v", playerId, changeList, err)
		return err
	}
	entry.Debugf("notify player rod :%+v", changeList)

	return nil
}

func ItemHeapNotify(ctx context.Context, playerId uint64, itemId int64, heapDurable int32) error {
	entry := logx.NewLogEntry(ctx)

	changeList := make(map[int64]int32)
	changeList[itemId] = heapDurable

	ntfBody := &hallPB.ItemHeapUpdateNotify{
		ItemHeaps: changeList,
	}
	err := sender.NotifyToPlayer(ctx, playerId, commonPB.MsgID_CMD_ITEM_HEAP_UPDATE_NTF, ntfBody)
	if err != nil {
		entry.Errorf("notify player[%d] itemHeap:%+v err:%+v", playerId, changeList, err)
		return err
	}
	entry.Debugf("notify player itemHeap :%+v", changeList)
	return nil
}

// PlayerRedDotNotify 推送玩家红点信息
func PlayerRedDotNotify(ctx context.Context, playerId uint64, redDotInfo []*hallPB.RedDotInfo) error {
	entry := logx.NewLogEntry(ctx)
	ntfBody := &hallPB.RedDotUpdateNotify{
		RedDots: redDotInfo,
	}
	err := sender.NotifyToPlayer(ctx, playerId, commonPB.MsgID_CMD_PLAYER_RED_DOT_UPDATE_NTF, ntfBody)
	if err != nil {
		entry.Errorf("notify player[%d] redDot:%+v err:%+v", playerId, redDotInfo, err)
		return err
	}
	entry.Debugf("notify player redDot :%+v", redDotInfo)
	return nil
}
