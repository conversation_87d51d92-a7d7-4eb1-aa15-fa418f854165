package logic_durable

import (
	"context"
	"errors"
	"fmt"
	daoItemHeap "hallsrv/internal/dao/dao_item_heap"
	logicItem "hallsrv/internal/logic/logic_item"
	logicNotify "hallsrv/internal/logic/logic_notify"
	modelItem "hallsrv/internal/model/model_item"
	rpcAsset "hallsrv/internal/repo/rpc_asset"
	"time"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
)

const MaxDurability = 10000

// QueryPlayerItemHeapDurable 查询玩家指定道具耐久
func QueryPlayerItemHeapDurable(ctx context.Context, playerId uint64, itemId int64) int32 {
	entry := logx.NewLogEntry(ctx)

	// 先查询redis
	itemIdHeapDurable, err := daoItemHeap.QueryItemHeapDurableRds(ctx, playerId, itemId)
	if errors.Is(err, redisx.Empty) {
		return 0
	}

	if itemIdHeapDurable != nil {
		return itemIdHeapDurable.DurablePer
	}

	// 再查询db
	itemIdHeapDurable, err = daoItemHeap.QueryItemHeapDurableRdb(ctx, playerId, itemId)
	if err != nil {
		entry.Errorf("query player:%d item:%d durable from db error: %+v", playerId, itemId, err)
		return 0
	}

	entry.Debugf("query player:%d item:%d durable: %+v", playerId, itemId, itemIdHeapDurable)

	if itemIdHeapDurable == nil {
		return 0
	}

	return itemIdHeapDurable.DurablePer
}

// QueryPlayerAllItemHeapDurable 查询玩家所有道具堆耐久
func QueryPlayerAllItemHeapDurable(ctx context.Context, playerId uint64) ([]*modelItem.TItemHeapDurable, error) {
	entry := logx.NewLogEntry(ctx)

	// 先查询redis
	itemIdHeapDurablesRds, err := daoItemHeap.QueryAllItemHeapDurableRds(ctx, playerId)
	if errors.Is(err, redisx.Empty) {
		return make([]*modelItem.TItemHeapDurable, 0), nil
	}

	if len(itemIdHeapDurablesRds) > 0 {
		return itemIdHeapDurablesRds, nil
	}

	// 再查询数据库
	itemIdHeapDurablesRdb, err := daoItemHeap.QueryAllItemHeapDurableRdb(ctx, playerId)
	if err != nil {
		entry.Errorf("query player:%d all item durable from db error: %+v", playerId, err)
		return nil, err
	}

	// 防击穿处理
	if len(itemIdHeapDurablesRdb) == 0 {
		redisx.GetGeneralCli().HSetNil(ctx, daoItemHeap.FmtItemHeapDurableRdsKey(playerId))
	} else {
		// 缓存到redis
		daoItemHeap.UpdateAllItemHeapDurableRds(ctx, playerId, itemIdHeapDurablesRdb)
	}

	entry.Debugf("query player:%d all item durable: %+v", playerId, itemIdHeapDurablesRdb)

	return itemIdHeapDurablesRdb, nil
}

// UpdatePlayerItemHeapDurable 更新玩家道具堆耐久
func UpdatePlayerItemHeapDurable(ctx context.Context, playerId uint64, itemId int64, durablePer int32) error {
	entry := logx.NewLogEntry(ctx)

	// 删除redis中的玩家信息
	err := daoItemHeap.ClearItemHeapDurableRds(ctx, playerId)
	if err != nil {
		entry.Warnf("delete player:%d item heap durable err:%+v", playerId, err)
		return err
	}

	// 延迟双删
	time.AfterFunc(3*time.Second, func() {
		if err := daoItemHeap.ClearItemHeapDurableRds(context.Background(), playerId); err != nil {
			entry.Warnf("delete player:%d item heap durable err:%+v", playerId, err)
		}
	})

	// 再更新数据库
	err = daoItemHeap.UpdateItemHeapDurableRdb(ctx, playerId, itemId, durablePer)
	if err != nil {
		entry.Errorf("update player:%d item:%d durable to db error: %+v", playerId, itemId, err)
		return err
	}

	entry.Debugf("update player:%d item:%d durable success", playerId, itemId)

	return nil
}

func ItemHeapToProto(hp []*modelItem.TItemHeapDurable) map[int64]int32 {
	itemHeap := make(map[int64]int32)
	if hp == nil {
		return itemHeap
	}
	for _, item := range hp {
		if item != nil {
			itemHeap[item.ItemId] = item.DurablePer
		}
	}
	return itemHeap
}

// 扣除鱼饵耐久
func LossItemHeap(ctx context.Context, playerId uint64, itemId int64, lossVal int32, sourceType commonPB.ITEM_SOURCE_TYPE) error {

	// TODO 移除
	productId := 1
	if sourceType == 0 {
		sourceType = commonPB.ITEM_SOURCE_TYPE_IST_ITEM_DURABILITY
	}
	// 检查背包是否存在道具
	queryItem, err := rpcAsset.GetPlayerItemListInfo(ctx, playerId, int32(productId), []int64{itemId}, commonPB.STORAGE_TYPE_ST_BAG)
	if err != nil {
		return protox.CodeError(commonPB.ErrCode_ERR_HALL_ITEM_NOT_ENOUGH, err.Error())
	}
	if len(queryItem) == 0 {
		return protox.CodeError(commonPB.ErrCode_ERR_HALL_ITEM_NOT_ENOUGH, fmt.Sprintf("item[%d] not found", itemId))
	}

	// 耐久度是否已经扣完
	currVal := QueryPlayerItemHeapDurable(ctx, playerId, itemId)
	if currVal >= MaxDurability {
		currVal = 0
	}
	isLoss := false
	newVal := currVal + lossVal
	if newVal >= MaxDurability {
		newVal = MaxDurability
		isLoss = true
	}

	if isLoss {
		// 耐久度扣完，删除道具
		_, err = logicItem.OperatePlayerItem(ctx, playerId, []*commonPB.ItemBase{{ItemId: itemId, ItemCount: 1}}, commonPB.ITEM_OPERATION_IO_REDUCE, sourceType, commonPB.STORAGE_TYPE_ST_BAG, false)
		if err != nil {
			return err
		}
	}

	// 更新耐久度
	err = UpdatePlayerItemHeapDurable(ctx, playerId, itemId, newVal)
	if err != nil {
		return err
	}
	logicNotify.ItemHeapNotify(ctx, playerId, itemId, newVal)
	return nil
}
