package logic_durable

import (
	test_init "hallsrv/internal/test"
	"testing"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
)

// 测试操作仓库
func TestOpStorage(t *testing.T) {
	test_init.Init()
	var playerId uint64 = 9000079
	ctx := test_init.NewCtxWithPlayerId(playerId)
	instanceIds := []string{"56bdddd3-ce63-11ef-8b9e-50ebf62e02d8"}
	_,err := FixStorageItem(ctx, playerId, instanceIds, commonPB.STORAGE_TYPE_ST_STORE)
	if err != nil {
		t.Fatalf("err %+v", err)
	}
}
