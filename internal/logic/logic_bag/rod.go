package logic_bag

import (
	"context"
	"fmt"
	"hallsrv/internal/config"
	"hallsrv/internal/dao/dao_trip_rod"
	logicItem "hallsrv/internal/logic/logic_item"
	logicNotify "hallsrv/internal/logic/logic_notify"
	"hallsrv/internal/model/model_bag"
	rpcTrip "hallsrv/internal/repo/rpc_trip"

	"git.keepfancy.xyz/back-end/frameworks/lib/timex"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	hallPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/hall"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	"git.keepfancy.xyz/back-end/frameworks/kit/dlm"
)

// GetAllRodBag 获取鱼竿背包信息
func GetAllRodBag(ctx context.Context, playerId uint64) ([]*model_bag.TTripRodGroup, error) {
	groups, err := dao_trip_rod.GetALL(ctx, playerId)
	if err != nil {
		return nil, err
	}

	return groups, nil
}
func rodBagDlmKey(playerId uint64) string {
	return fmt.Sprintf(config.RDS_KEY_ROD_BAG_LOCK, playerId)
}

// 检查是否在场内
func checkInRoom(ctx context.Context, playerId uint64) (inFlag bool, err error) {
	roomInfo, err := rpcTrip.RpcGetPlayerRoomInfo(ctx, playerId)
	if err != nil {
		return false, err
	}
	return roomInfo != nil, nil
}

// 更新装备
func UpdateRodBag(ctx context.Context, playerId uint64, req *hallPB.UpdateTripRodReq) (*model_bag.TTripRodGroup, error) {
	entry := logx.NewLogEntry(ctx)
	itemId := req.GetItemId()
	id := req.GetId()
	instanceId := req.GetInstanceId()
	sit := req.GetSit()
	var err error
	delStoreItems := make([]*commonPB.OriginLoot, 0)

	// 先检查是否在场内
	// inFlag, err := checkInRoom(ctx, playerId)
	// if err != nil {
	// 	return nil, err
	// }
	bagItems := make([]*commonPB.ItemInfo, 0)
	storeItems := make([]*commonPB.ItemInfo, 0)
	delItem := &commonPB.OriginLoot{}
	// 卸下道具store不用检查
	if itemId != 0 {
		storeItems, err = logicItem.PlayerQueryItemInfoByIdList(ctx, playerId, []int64{itemId})
		if err != nil {
			return nil, err
		}
		itemInfo := cmodel.GetItem(req.GetItemId())
		if itemInfo == nil {
			return nil, protox.CodeError(commonPB.ErrCode_ERR_CONF_ERROR, fmt.Sprintf("item[%d] not found", itemId))
		}
		// 不使用背包数据
		delItem, _ = selectRodItem(storeItems, bagItems, itemId, instanceId)
		if delItem == nil {
			return nil, fmt.Errorf("has some item[%d] not found", itemId)
		}
		delStoreItems = append(delStoreItems, delItem)
	}

	unlock := dlm.LockKey(rodBagDlmKey(playerId))
	defer unlock()

	// 获取钓组信息
	group, err := dao_trip_rod.Get(ctx, playerId, id)
	if err != nil {
		return nil, err
	}
	// 退原有装备
	oldOne := group.Data[sit]
	oldItem := &commonPB.Item{}
	if oldOne != nil {
		oldItem = oldOne.ToItem(ctx)
		if oldItem == nil {
			return nil, protox.CodeError(commonPB.ErrCode_ERR_CONF_ERROR, fmt.Sprintf("item[%d] not found", oldOne.Id))
		}
	}

	group.SetRodByItem(sit, delItem.GetItem())

	engine, err := dao_trip_rod.GetSqlEngine()
	if err != nil {
		return nil, err
	}

	session := engine.NewSession()
	// 扣装备

	// 更新
	err = dao_trip_rod.Update(ctx, session, group)
	if err != nil {
		errRollback := session.Rollback()
		if errRollback != nil {
			return nil, errRollback
		}
		return nil, err
	}

	// 扣装备
	if len(delStoreItems) > 0 {
		reward, err := logicItem.OperateItemByInstance(ctx, playerId, delStoreItems, commonPB.ITEM_OPERATION_IO_REDUCE, commonPB.STORAGE_TYPE_ST_STORE, commonPB.ITEM_SOURCE_TYPE_IST_BAG_MOVE)
		_ = reward
		if err != nil {
			entry.Errorf("del item fail:%+v", err)
			errRollBack := session.Rollback()
			if errRollBack != nil {
				return nil, errRollBack
			}
			return nil, err
		}
		entry.Infof("delStoreItem:%+v", delStoreItems)
	}

	if oldItem.InstanceId != "" {
		// 为了实现自动堆叠，需要把instance临时去掉
		oldItem.InstanceId = ""
		reward, err := logicItem.OperateItemByInstance(ctx, playerId, []*commonPB.OriginLoot{{Item: oldItem, Value: 1}}, commonPB.ITEM_OPERATION_IO_ADD, commonPB.STORAGE_TYPE_ST_STORE, commonPB.ITEM_SOURCE_TYPE_IST_BAG_MOVE)
		_ = reward
		if err != nil {
			entry.Errorf("return store item fail:%+v", err)
			errRollBack := session.Rollback()
			if errRollBack != nil {
				return nil, errRollBack
			}
			return nil, err
		}
		entry.Infof("addStoreItem:%+v from rod:%d", oldItem, id)
	}

	session.Commit()

	logicNotify.PlayerRodRigNotify(ctx, playerId, group)

	return group, nil
}

// 检查 钓组变化
func checkRodChange(src map[int32]*model_bag.TTripRod, dst map[int32]*model_bag.TTripRod) (del, add []*model_bag.TTripRod) {
	// XXX 默认长度一致
	del = make([]*model_bag.TTripRod, 0)
	add = make([]*model_bag.TTripRod, 0)
	for index, srcOne := range src {
		destOne := dst[index]
		if srcOne.ItemId != destOne.ItemId || srcOne.InstanceId != destOne.InstanceId {
			del = append(del, srcOne)
			add = append(add, destOne)
		}
	}
	return del, add
}

// 筛选上阵道具
func selectRodItem(storeItems []*commonPB.ItemInfo, bagItems []*commonPB.ItemInfo, itemId int64, instanceId string) (DelItems *commonPB.OriginLoot, storage commonPB.STORAGE_TYPE) {

	// 暂时不考虑同一套装备能重复装相同的道具
	// 检查仓库是否有满足条件道具

	item := getRodItem(storeItems, itemId, instanceId)
	if item != nil {
		return &commonPB.OriginLoot{Item: item, Value: 1}, commonPB.STORAGE_TYPE_ST_STORE
	}
	item = getRodItem(bagItems, itemId, instanceId)
	if item != nil {
		return &commonPB.OriginLoot{Item: item, Value: 1}, commonPB.STORAGE_TYPE_ST_BAG
	}

	return nil, commonPB.STORAGE_TYPE_ST_UNKNOWN
}

func getRodItem(stocks []*commonPB.ItemInfo, itemId int64, instanceId string) *commonPB.Item {
	// TODO: 对装备优劣先排序
	for _, item := range stocks {
		if item.Item.ItemId == itemId {
			if instanceId != "" {
				if instanceId == item.Item.InstanceId {
					return item.Item
				}
			} else {
				return item.Item
			}
		}
	}
	return nil
}

// SaveNewRodStore 存入仓库
func SaveNewRodStore(ctx context.Context, playerId uint64, req *hallPB.SaveNewTripRodReq) (info *commonPB.RodBagInfo, err error) {
	entry := logx.NewLogEntry(ctx)

	err = CheckNameForbid(ctx, playerId, req.Name)
	if err != nil {
		entry.Warnf("SaveNewRodStore player:%d rod rig info:%+v has forbid error:%+v", playerId, req, err)
		return nil, err
	}

	items, err := CheckRodSit(ctx, playerId, req.List)
	if err != nil {
		entry.Warnf("SaveNewRodStore player:%d rod rig info:%+v has sit error:%+v", playerId, req, err)
		return nil, err
	}
	storeItems, err := logicItem.PlayerQueryItemInfoByIdList(ctx, playerId, items)
	if err != nil {
		return nil, err
	}
	newRig := &model_bag.TTripRodGroup{
		PlayerId: playerId,
		Data:     make(map[int32]*model_bag.TTripRod),
		Name:     req.Name,
		UpdateAt: timex.Now().Unix(),
	}

	delStoreItems := make([]*commonPB.OriginLoot, 0)
	for _, item := range req.List {
		delItem, bagType := selectRodItem(storeItems, []*commonPB.ItemInfo{}, item.ItemId, item.InstanceId)

		if delItem == nil {
			return nil, fmt.Errorf("has some item[%d] not found", item.ItemId)
		}
		if bagType != commonPB.STORAGE_TYPE_ST_STORE {
			continue
		}
		delStoreItems = append(delStoreItems, delItem)
		newRig.SetRodByItem(item.Sit, delItem.Item)
	}

	engine, err := dao_trip_rod.GetSqlEngine()
	if err != nil {
		return nil, err
	}

	session := engine.NewSession()
	err = dao_trip_rod.Insert(ctx, session, newRig)
	if err != nil {
		errRollback := session.Rollback()
		if errRollback != nil {
			return nil, errRollback
		}
		return nil, err
	}

	// 扣仓库
	if len(delStoreItems) > 0 {
		reward, err := logicItem.OperateItemByInstance(ctx, playerId, delStoreItems, commonPB.ITEM_OPERATION_IO_REDUCE, commonPB.STORAGE_TYPE_ST_STORE, commonPB.ITEM_SOURCE_TYPE_IST_BAG_MOVE)
		_ = reward
		if err != nil {
			entry.Errorf("del item fail:%+v", err)
			errRollBack := session.Rollback()
			if errRollBack != nil {
				return nil, errRollBack
			}
			return nil, err
		}
		entry.Infof("delStoreItem:%+v", delStoreItems)
	}

	session.Commit()

	logicNotify.PlayerRodRigNotify(ctx, playerId, newRig)
	return newRig.ToProto(ctx), nil
}

// SplitRodBag 拆卸杆组
func SplitRodBag(ctx context.Context, playerId uint64, id int32) error {
	entry := logx.NewLogEntry(ctx)
	rodGroup, err := dao_trip_rod.Get(ctx, playerId, id)
	if err != nil {
		return err
	}

	// 还带instanceId
	backItem := make([]*commonPB.Item, 0)
	for _, rod := range rodGroup.Data {
		if rod.InstanceId != "" {
			backItem = append(backItem, rod.ToItem(ctx))
		}
	}

	engine, err := dao_trip_rod.GetSqlEngine()
	if err != nil {
		return err
	}

	// 上分布式锁
	unlock := dlm.LockKey(rodBagDlmKey(playerId))
	defer unlock()

	session := engine.NewSession()
	defer session.Close()

	session.Begin()

	// 2pc
	// 预删除
	err = dao_trip_rod.Del(ctx, session, playerId, id)
	if err != nil {
		errRollBack := session.Rollback()
		if errRollBack != nil {
			return errRollBack
		}

		return err
	}

	// 批量还道具
	lootList := makeBackItem(ctx, backItem)
	if len(lootList) > 0 {
		_, err = logicItem.OperateItemByInstance(ctx, playerId, lootList, commonPB.ITEM_OPERATION_IO_ADD, commonPB.STORAGE_TYPE_ST_STORE, commonPB.ITEM_SOURCE_TYPE_IST_BAG_MOVE)
		if err != nil {
			entry.Errorf("SplitRodBag return bag item fail:%+v", err)
			errRollBack := session.Rollback()
			if errRollBack != nil {
				return errRollBack
			}
			return err
		}
		entry.Infof("SplitRodBag addStoreItem:%+v from rod:%d", lootList, id)
	}

	session.Commit()

	return nil
}

func CheckNameForbid(ctx context.Context, playerId uint64, name string) error {
	// if name == "" {
	// 	return protox.CodeError(commonPB.ErrCode_ERR_BAD_PARAM, "rod name is empty")
	// }

	// // 校验屏蔽字库
	// if name != "" {
	// 	existForbid := forbid.ExistForbid(name)
	// 	if existForbid {
	// 		return protox.CodeError(commonPB.ErrCode_ERR_FORBID_WORD, "rod name forbid")
	// 	}
	// }
	return nil
}

// 构造归还道具
func makeBackItem(ctx context.Context, items []*commonPB.Item) []*commonPB.OriginLoot {
	lootList := make([]*commonPB.OriginLoot, 0)
	for _, item := range items {
		// 为了实现自动堆叠，需要把instance临时去掉
		// 仅针对钩饵合并，其他装备不合并
		switch item.ItemType {
		case commonPB.ITEM_TYPE_IT_TACKLE_HOOKS, commonPB.ITEM_TYPE_IT_TACKLE_BAIT, commonPB.ITEM_TYPE_IT_TACKLE_LURES:
			item.InstanceId = ""
		}
		lootList = append(lootList, &commonPB.OriginLoot{Item: item, Value: 1})
	}
	return lootList
}

func CheckRodSit(ctx context.Context, playerId uint64, list []*hallPB.UpdateTripRodReq) ([]int64, error) {
	itemIds := make([]int64, 0)
	for _, v := range list {
		// switch commonPB.TRIP_ROD_SIT(v.Sit) {
		// case commonPB.TRIP_ROD_SIT_TRS_HOOKS, commonPB.TRIP_ROD_SIT_TRS_BAIT:
		// 	return nil, protox.CodeError(commonPB.ErrCode_ERR_BAD_PARAM, "rod sit error")
		// }
		if v.ItemId != 0 && v.ItemId != -1 {
			itemIds = append(itemIds, int64(v.ItemId))
		}
	}
	return itemIds, nil
}

// BatchUpdateRodBag 批量更新装备
func BatchUpdateRodBag(ctx context.Context, playerId uint64, req *hallPB.BatchUpdateTripRodReq) (*model_bag.TTripRodGroup, error) {
	entry := logx.NewLogEntry(ctx)
	id := req.GetId()

	err := CheckNameForbid(ctx, playerId, req.Name)
	if err != nil {
		entry.Warnf("BatchUpdateRodBag player:%d rod rig info:%+v has forbid error:%+v", playerId, req, err)
		return nil, err
	}

	items, err := CheckRodSit(ctx, playerId, req.List)
	if err != nil {
		entry.Warnf("BatchUpdateRodBag player:%d rod rig info:%+v has sit error:%+v", playerId, req, err)
		return nil, err
	}

	// 如果items为空，则不查询仓库(以为全是卸下)
	storeItems := make([]*commonPB.ItemInfo, 0)
	if len(items) > 0 {
		storeItems, err = logicItem.PlayerQueryItemInfoByIdList(ctx, playerId, items)
		if err != nil {
			return nil, err
		}
	}

	itemInfo := cmodel.GetAllItem(consul_config.WithGrpcCtx(ctx))
	if itemInfo == nil {
		return nil, protox.CodeError(commonPB.ErrCode_ERR_CONF_ERROR, "itemCfg not found")
	}

	// 场内额外检查
	// if inFlag && len(items) > 0 {
	// 	ruleMap := make(map[int32]struct{})
	// 	// 检查背包类型可用性
	// 	checkRule := cmodel.GetAllTripBagStoreRule(consul_config.WithGrpcCtx(ctx))
	// 	for _, rule := range checkRule {
	// 		if rule.BagType == int32(commonPB.TRIP_BAG_TYPE_TBT_FISHING_GEAR) {
	// 			ruleMap[rule.ItemType] = struct{}{}
	// 		}
	// 	}
	// 	for _, itemId := range items {
	// 		if _, ok := ruleMap[itemInfo[itemId].ItemType]; !ok {
	// 			return nil, protox.CodeError(commonPB.ErrCode_ERR_BAD_PARAM, fmt.Sprintf("item[%d] can not change in pond", itemId))
	// 		}
	// 	}
	// }

	unlock := dlm.LockKey(rodBagDlmKey(playerId))
	defer unlock()

	// bagItems, err := GetTripBagItem(ctx, playerId, commonPB.TRIP_BAG_TYPE_TBT_FISHING_GEAR)
	// if err != nil {
	// 	return nil, err
	// }
	bagItems := make([]*commonPB.ItemInfo, 0)

	// 获取钓组信息
	group, err := dao_trip_rod.Get(ctx, playerId, id)
	if err != nil {
		return nil, err
	}

	delStoreItems := make([]*commonPB.OriginLoot, 0)
	oldItems := make([]*commonPB.Item, 0)

	for _, item := range req.List {
		oldOne := group.Data[item.Sit]
		if oldOne != nil && oldOne.ItemId > 0 && oldOne.InstanceId != "" {
			oldItem := oldOne.ToItem(ctx)
			if oldItem == nil {
				return nil, protox.CodeError(commonPB.ErrCode_ERR_CONF_ERROR, fmt.Sprintf("item[%d] not found", oldOne.Id))
			}
			oldItems = append(oldItems, oldItem)
		}
		// 卸下
		if item.ItemId == 0 || item.ItemId == -1 {
			// 本身道具不存在
			if group.Data[item.Sit] == nil {
				continue
			}
			// group.Data[item.Sit].FromItem(&commonPB.Item{
			// 	// 卸下道具
			// 	ItemId: item.ItemId,
			// })
			delete(group.Data, item.Sit)
			continue
		}

		delItem, bagType := selectRodItem(storeItems, bagItems, item.ItemId, item.InstanceId)

		if delItem == nil {
			return nil, fmt.Errorf("has some item[%d] not found", item.ItemId)
		}
		if bagType == commonPB.STORAGE_TYPE_ST_STORE {
			delStoreItems = append(delStoreItems, delItem)
		}

		// 初始化结构
		group.SetRodByItem(item.Sit, delItem.Item)
	}

	engine, err := dao_trip_rod.GetSqlEngine()
	if err != nil {
		return nil, err
	}

	session := engine.NewSession()
	// 扣装备

	// 更新
	group.Name = req.Name
	err = dao_trip_rod.Update(ctx, session, group)
	if err != nil {
		errRollback := session.Rollback()
		if errRollback != nil {
			return nil, errRollback
		}
		return nil, err
	}

	// 扣装备
	if len(delStoreItems) > 0 {
		reward, err := logicItem.OperateItemByInstance(ctx, playerId, delStoreItems, commonPB.ITEM_OPERATION_IO_REDUCE, commonPB.STORAGE_TYPE_ST_STORE, commonPB.ITEM_SOURCE_TYPE_IST_BAG_MOVE)
		_ = reward
		if err != nil {
			entry.Errorf("del item fail:%+v", err)
			errRollBack := session.Rollback()
			if errRollBack != nil {
				return nil, errRollBack
			}
			return nil, err
		}
		entry.Infof("delStoreItem:%+v", delStoreItems)
	}

	backItems := makeBackItem(ctx, oldItems)
	if len(backItems) > 0 {
		reward, err := logicItem.OperateItemByInstance(ctx, playerId, backItems, commonPB.ITEM_OPERATION_IO_ADD, commonPB.STORAGE_TYPE_ST_STORE, commonPB.ITEM_SOURCE_TYPE_IST_BAG_MOVE)
		_ = reward
		if err != nil {
			entry.Errorf("return store item fail:%+v", err)
			errRollBack := session.Rollback()
			if errRollBack != nil {
				return nil, errRollBack
			}
			return nil, err
		}
		entry.Infof("addStoreItem:%+v from rod:%d", backItems, id)
	}

	session.Commit()

	logicNotify.PlayerRodRigNotify(ctx, playerId, group)
	return group, nil
}
