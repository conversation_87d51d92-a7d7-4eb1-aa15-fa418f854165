package logic_bag

import (
	"context"
	hallPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/hall"
	test_init "hallsrv/internal/test"
	"testing"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
)

func TestSaveRodBag(t *testing.T) {
	test_init.InitRedisConsul()
	test_init.InitSql()

	type args struct {
		ctx      context.Context
		playerId uint64
		rigId    int32
	}
	tests := []struct {
		name string
		args args
	}{
		// TODO: Add test cases.
		{
			args: args{
				ctx:      test_init.NewCtx(),
				playerId: 9000050,
				rigId:    1,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotInfo, err := SaveRodStore(tt.args.ctx, tt.args.playerId, tt.args.rigId)
			if err != nil {
				t.<PERSON>("err =%v", err)
			}
			t.Logf("gotInfo = %+v", gotInfo)
		})
	}
}

func TestPutRodBag(t *testing.T) {
	test_init.InitRedisConsul()
	test_init.InitSql()

	type args struct {
		ctx      context.Context
		playerId uint64
		id       int32
		bagIndex int32
	}
	tests := []struct {
		name     string
		args     args
		wantInfo *commonPB.RodBagInfo
		wantErr  bool
	}{
		// TODO: Add test cases.
		{
			args: args{
				ctx:      test_init.NewCtx(),
				playerId: 9000050,
				id:       1,
				bagIndex: 2,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotInfo, err := PutRodBag(tt.args.ctx, tt.args.playerId, tt.args.id, tt.args.bagIndex)
			if err != nil {
				t.Errorf("err =%v", err)
			}
			t.Logf("gotInfo = %+v", gotInfo)
		})
	}
}

func TestLossDurability(t *testing.T) {
	test_init.Init()

	ctx := test_init.NewCtx()
	kv := map[int32]int64{
		1: 9999999,
		2: 20,
	}

	err := LossRodDurability(ctx, 9000079, 1, kv)
	if err != nil {
		t.Fatalf("err =%v", err)
	}
}

func TestDelRodBag(t *testing.T) {
	test_init.InitRedisConsul()
	test_init.InitSql()

	type args struct {
		ctx      context.Context
		playerId uint64
		id       int32
	}
	tests := []struct {
		name     string
		args     args
		wantInfo *commonPB.RodBagInfo
		wantErr  bool
	}{
		// TODO: Add test cases.
		{
			args: args{
				ctx:      test_init.NewCtx(),
				playerId: 9000050,
				id:       1,
			},
		},
	}
	_ = tests
	// for _, tt := range tests {
	// 	t.Run(tt.name, func(t *testing.T) {
	// 		gotInfo, err := DelRodBag(tt.args.ctx, tt.args.playerId, tt.args.id)
	// 		if (err != nil) != tt.wantErr {
	// 			t.Errorf("DelRodBag() error = %v, wantErr %v", err, tt.wantErr)
	// 			return
	// 		}
	// 		if !reflect.DeepEqual(gotInfo, tt.wantInfo) {
	// 			t.Errorf("DelRodBag() gotInfo = %v, want %v", gotInfo, tt.wantInfo)
	// 		}
	// 	})
	// }
}

func TestSplitRodBag(t *testing.T) {
	test_init.InitRedisConsul()
	test_init.InitSql()
	type args struct {
		ctx      context.Context
		playerId uint64
		id       int32
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			args: args{
				ctx:      test_init.NewCtx(),
				playerId: 9000050,
				id:       2,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := SplitRodBag(tt.args.ctx, tt.args.playerId, tt.args.id); (err != nil) != tt.wantErr {
				t.Errorf("SplitRodBag() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestSaveNewRodStore(t *testing.T) {
	test_init.InitRedisConsul()
	test_init.InitSql()
	type args struct {
		ctx      context.Context
		playerId uint64
		req      *hallPB.SaveNewTripRodReq
	}
	tests := []struct {
		name     string
		args     args
		wantInfo *commonPB.RodBagInfo
		wantErr  bool
	}{
		// TODO: Add test cases.
		{
			args: args{
				ctx:      test_init.NewCtx(),
				playerId: 1520,
				req: &hallPB.SaveNewTripRodReq{
					Name: "浪尖F360-MH",
					List: []*hallPB.UpdateTripRodReq{
						{
							Sit:        1,
							ItemId:     3015001,
							InstanceId: "f8a2a380-fe50-11ef-b842-0ac634529125",
							Storage:    commonPB.STORAGE_TYPE_ST_STORE,
						},
						{
							Sit:        2,
							ItemId:     3021002,
							InstanceId: "f8a2a3e4-fe50-11ef-b842-0ac634529125",
							Storage:    commonPB.STORAGE_TYPE_ST_STORE,
						},
						{
							Sit:        3,
							ItemId:     3031001,
							InstanceId: "f8a2a27c-fe50-11ef-b842-0ac634529125",
							Storage:    commonPB.STORAGE_TYPE_ST_STORE,
						},
						{
							Sit:        6,
							ItemId:     3065001,
							InstanceId: "f8a2a312-fe50-11ef-b842-0ac634529125",
							Storage:    commonPB.STORAGE_TYPE_ST_STORE,
						},
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotInfo, err := SaveNewRodStore(tt.args.ctx, tt.args.playerId, tt.args.req)
			t.Logf("err =%v", err)
			t.Logf("gotInfo = %+v", gotInfo)
		})
	}
}
