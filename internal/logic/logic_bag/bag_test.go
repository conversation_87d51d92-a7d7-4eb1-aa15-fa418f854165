package logic_bag

import (
	"context"
	test_init "hallsrv/internal/test"
	"testing"
)

func TestUnloadTripBag(t *testing.T) {
	test_init.InitRedisConsul()
	test_init.InitSql()

	type args struct {
		ctx      context.Context
		playerId uint64
	}
	tests := []struct {
		name string
		args args
	}{
		// TODO: Add test cases.
		{
			args: args{
				ctx:      test_init.NewCtx(),
				playerId: 9000050,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := UnloadAllTripBag(tt.args.ctx, tt.args.playerId); err != nil {
				t.<PERSON>("UnloadAllTripBag() error = %v", err)
			}
		})
	}
}
