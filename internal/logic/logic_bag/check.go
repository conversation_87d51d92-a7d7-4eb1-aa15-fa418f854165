package logic_bag

import (
	"context"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
)

// CheckPlayerBagSpace 检查玩家背包空间
func CheckPlayerBagSpace(ctx context.Context, playerId uint64, opt commonPB.ITEM_OPERATION, changeList []*commonPB.ItemBase) error {
	var bagType commonPB.TRIP_BAG_TYPE

	// 判断道具类型
	for _, item := range changeList {
		// 道具信息
		itemConf := cmodel.GetItem(item.GetItemId(), consul_config.WithGrpcCtx(ctx))
		if itemConf != nil {
			if int32(commonPB.ITEM_TYPE_IT_PROP_FOOD) == itemConf.ItemType {
				bagType = commonPB.TRIP_BAG_TYPE_TBT_FOOD
			} else {
				bagType = commonPB.TRIP_BAG_TYPE_TBT_FISHING_GEAR
			}
		}
	}

	itemList, err := GetTripBagItem(ctx, playerId, bagType)
	if err != nil {
		return err
	}

	err = checkBagHeap(ctx, itemList, bagType, changeList)
	if err != nil {
		return err
	}

	return nil
}
