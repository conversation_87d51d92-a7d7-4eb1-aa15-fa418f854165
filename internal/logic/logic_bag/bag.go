package logic_bag

import (
	"context"
	"errors"
	"fmt"
	"hallsrv/internal/dao/dao_trip_rod"
	logicNotify "hallsrv/internal/logic/logic_notify"
	"hallsrv/internal/model/model_bag"
	modelItem "hallsrv/internal/model/model_item"
	rpcAsset "hallsrv/internal/repo/rpc_asset"
	"math"

	"git.keepfancy.xyz/back-end/frameworks/kit/rpc/interceptor"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
)

func bagTypeCategory(bagType commonPB.TRIP_BAG_TYPE) []commonPB.ITEM_CATEGORY {
	list := make([]commonPB.ITEM_CATEGORY, 0)
	switch bagType {
	case commonPB.TRIP_BAG_TYPE_TBT_FOOD:
		list = append(list, commonPB.ITEM_CATEGORY_IC_PROP)
	case commonPB.TRIP_BAG_TYPE_TBT_FISHING_GEAR:
		list = append(list, commonPB.ITEM_CATEGORY_IC_TACKLE)
	}
	return list
}

func GetTripBagItem(ctx context.Context, playerId uint64, bagType commonPB.TRIP_BAG_TYPE) (list []*commonPB.ItemInfo, err error) {
	entry := logx.NewLogEntry(ctx)
	categoryList := bagTypeCategory(bagType)
	itemList, err := rpcAsset.GetPlayerCategoryListItemInfo(ctx, playerId, interceptor.GetRPCOptions(ctx).ProductId, categoryList, commonPB.STORAGE_TYPE_ST_BAG)
	if err != nil {
		entry.Errorf("query player:%d, categoryList:%v, err:%+v", playerId, categoryList, err)
		return nil, err
	}

	return itemList, nil
}

// ModifyTripBag 修改旅行背包
func ModifyTripBag(ctx context.Context, playerId uint64, bagType commonPB.TRIP_BAG_TYPE, opt commonPB.ITEM_OPERATION, changeList []*commonPB.ItemBase) (err error) {
	// 校验数据合法性
	for _, item := range changeList {
		if br := checkCanInsert(ctx, bagType, item.GetItemId()); !br {
			return fmt.Errorf("can not insert bag:%+v item:%+v", bagType, item)
		}
	}

	// 原有资源
	switch opt {
	// TODO: 修改成2PC
	case commonPB.ITEM_OPERATION_IO_ADD:
		// 从仓库到背包
		return FromStore2Bag(ctx, playerId, bagType, opt, changeList)
	case commonPB.ITEM_OPERATION_IO_REDUCE:
		// 从背包到仓库
		return FromBag2Store(ctx, playerId, bagType, opt, changeList)
	default:
		return errors.New("invalid operation")
	}

}

// 从背包到仓库
func FromBag2Store(ctx context.Context, playerId uint64, bagType commonPB.TRIP_BAG_TYPE, opt commonPB.ITEM_OPERATION, changeList []*commonPB.ItemBase) error {
	// entry := logx.NewLogEntry(ctx)

	itemParamList := make([]*modelItem.ItemOptParam, 0)
	for _, item := range changeList {
		itemParam, err := modelItem.NewItemOptParam(ctx, item.GetItemId(), item.GetItemCount(), false)
		if err != nil {
			return fmt.Errorf("item params fail:%+v", err)
		}
		itemParamList = append(itemParamList, itemParam...)
	}

	fromChange, toChange, err := rpcAsset.PlayerMoveItemReq(ctx, playerId, commonPB.STORAGE_TYPE_ST_BAG, commonPB.STORAGE_TYPE_ST_STORE, itemParamList)
	if err != nil {
		return err
	}

	// 移出背包
	rewardFromInfo := &commonPB.Reward{
		ItemList: fromChange,
	}
	logicNotify.PlayerRewardInfoNotify(ctx, playerId, commonPB.STORAGE_TYPE_ST_BAG, rewardFromInfo)

	// 移入仓库
	rewardToInfo := &commonPB.Reward{
		ItemList: toChange,
	}
	logicNotify.PlayerRewardInfoNotify(ctx, playerId, commonPB.STORAGE_TYPE_ST_STORE, rewardToInfo)

	return nil
}

// 从仓库到背包
func FromStore2Bag(ctx context.Context, playerId uint64, bagType commonPB.TRIP_BAG_TYPE, opt commonPB.ITEM_OPERATION, changeList []*commonPB.ItemBase) error {
	// 检查背包格子

	itemList, err := GetTripBagItem(ctx, playerId, bagType)
	if err != nil {
		return err
	}

	err = checkBagHeap(ctx, itemList, bagType, changeList)
	if err != nil {
		return err
	}
	itemParamList := make([]*modelItem.ItemOptParam, 0)
	for _, item := range changeList {
		itemParam, err := modelItem.NewItemOptParam(ctx, item.GetItemId(), item.GetItemCount(), false)
		if err != nil {
			return fmt.Errorf("item params fail:%+v", err)
		}
		itemParamList = append(itemParamList, itemParam...)
	}

	fromChange, toChange, err := rpcAsset.PlayerMoveItemReq(ctx, playerId, commonPB.STORAGE_TYPE_ST_STORE, commonPB.STORAGE_TYPE_ST_BAG, itemParamList)
	if err != nil {
		return err
	}

	// 移出仓库
	rewardFromInfo := &commonPB.Reward{
		ItemList: fromChange,
	}
	logicNotify.PlayerRewardInfoNotify(ctx, playerId, commonPB.STORAGE_TYPE_ST_STORE, rewardFromInfo)

	// 移入背包
	rewardToInfo := &commonPB.Reward{
		ItemList: toChange,
	}
	logicNotify.PlayerRewardInfoNotify(ctx, playerId, commonPB.STORAGE_TYPE_ST_BAG, rewardToInfo)

	return nil
}

// 检查背包插入有效性
func checkCanInsert(ctx context.Context, bagType commonPB.TRIP_BAG_TYPE, itemId int64) bool {
	entry := logx.NewLogEntry(ctx)
	for _, rule := range cmodel.GetAllTripBagStoreRule(consul_config.WithGrpcCtx(ctx)) {
		// 相同背包类型
		if rule.BagType == int32(bagType) {
			cfg := cmodel.GetItem(int64(itemId), consul_config.WithGrpcCtx(ctx))
			if cfg == nil {
				entry.Errorf("unknown itemId:%+v", itemId)
				return false
			}

			// 相同道具类型
			if cfg.ItemType == rule.ItemType {
				return true
			}
		}
	}
	return false
}

func toItemBase(item *commonPB.ItemInfo) *commonPB.ItemBase {
	return &commonPB.ItemBase{
		ItemId:    item.Item.ItemId,
		ItemCount: item.ItemDeltaCount,
	}
}

// 检查堆叠
func checkBagHeap(ctx context.Context, stocks []*commonPB.ItemInfo, bagType commonPB.TRIP_BAG_TYPE, changeList []*commonPB.ItemBase) (err error) {
	entry := logx.NewLogEntry(ctx)
	cfg := cmodel.GetItemConst(consul_config.WithGrpcCtx(ctx))
	var maxHeap int32 = 0
	switch bagType {
	case commonPB.TRIP_BAG_TYPE_TBT_FISHING_GEAR:
		maxHeap = int32(cfg.TripBagTackleCells)
	case commonPB.TRIP_BAG_TYPE_TBT_FOOD:
		maxHeap = int32(cfg.TripBagFoodCells)
	default:
		entry.Errorf("invalid bag type :%+v", bagType)
		return fmt.Errorf("invalid bag type :%+v", bagType)
	}
	// 聚合统计
	sum := make(map[int64]int64, 0)
	for _, item := range stocks {
		sum[item.Item.ItemId] += item.ItemCount
	}

	for _, item := range changeList {
		sum[item.ItemId] += item.ItemCount
	}

	// 拆分计算堆叠
	stackCal := 0
	for itemId, itemCount := range sum {
		stackMax := getStack(ctx, itemId)
		if stackMax == 0 {
			stackCal += 1
		} else {
			// 拆堆
			stackNum := math.Ceil(float64(itemCount) / float64(stackMax))
			stackCal += int(stackNum)
		}
	}
	if stackCal > int(maxHeap) {
		return fmt.Errorf("overlay bag size:[%d]", bagType)
	}

	return nil
}

// 获取堆叠数量
func getStack(ctx context.Context, itemId int64) int64 {
	entry := logx.NewLogEntry(ctx)
	cfg := cmodel.GetItem(itemId, consul_config.WithGrpcCtx(ctx))
	if cfg == nil {
		entry.Errorf("itemId[%d] config not found", itemId)
		return 0
	}
	return int64(cfg.Stack)
}

func absInt64(a int64) int64 {
	if a < 0 {
		return -1 * a
	}
	return a
}

// UnloadAllTripBag 卸下背包所有物品
func UnloadAllTripBag(ctx context.Context, playerId uint64) (err error) {
	// todo  抽象公共代码
	// 卸下渔具包
	fishList, err := GetTripBagItem(ctx, playerId, commonPB.TRIP_BAG_TYPE_TBT_FISHING_GEAR)
	if err != nil {
		return err
	}
	fishItems := make([]*commonPB.ItemBase, 0, len(fishList))
	for _, fishInfo := range fishList {
		fishInfo.ItemDeltaCount = fishInfo.ItemCount
		fishItems = append(fishItems, toItemBase(fishInfo))
	}

	if len(fishList) != 0 {
		err = FromBag2Store(ctx, playerId, commonPB.TRIP_BAG_TYPE_TBT_FISHING_GEAR, commonPB.ITEM_OPERATION_IO_REDUCE, fishItems)
		if err != nil {
			return err
		}
	}

	// 卸下食物包
	foodList, err := GetTripBagItem(ctx, playerId, commonPB.TRIP_BAG_TYPE_TBT_FOOD)
	if err != nil {
		return err
	}
	foodItems := make([]*commonPB.ItemBase, 0, len(foodList))
	for _, foodInfo := range foodList {
		foodInfo.ItemDeltaCount = foodInfo.ItemCount
		foodItems = append(foodItems, toItemBase(foodInfo))
	}

	if len(foodList) != 0 {
		err = FromBag2Store(ctx, playerId, commonPB.TRIP_BAG_TYPE_TBT_FOOD, commonPB.ITEM_OPERATION_IO_REDUCE, foodItems)
		if err != nil {
			return err
		}
	}

	arr, err := GetAllRodBag(ctx, playerId)
	if err != nil {
		return err
	}

	// 卸下杆组
	engine, err := dao_trip_rod.GetSqlEngine()
	if err != nil {
		return err
	}
	session := engine.NewSession()
	defer session.Close()

	session.Begin()

	// 2pc
	// 卸下
	err = dao_trip_rod.UnloadAll(ctx, session, playerId)
	if err != nil {
		errRollBack := session.Rollback()
		if errRollBack != nil {
			return errRollBack
		}

		return err
	}
	session.Commit()

	rods := make([]*model_bag.TTripRodGroup, 0)
	for _, rod := range arr {
		if rod.BagIndex > 0 {
			rod.BagIndex = 0
			rods = append(rods, rod)
		}
	}

	logicNotify.PlayerRodRigNotify(ctx, playerId, rods...)

	return
}
