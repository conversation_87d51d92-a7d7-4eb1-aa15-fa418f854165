package logic_ticket

import (
	"context"
	"fmt"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	dao_ticket "hallsrv/internal/dao/dao_ticket"
)

// GetCurPondTicket 查询多个钓场的门票信息
func GetCurPondTicket(ctx context.Context, playerId uint64, pondIds []int64) ([]*commonPB.TicketInfo, error) {
	if playerId == 0 || len(pondIds) == 0 {
		return nil, fmt.Errorf("param error")
	}
	m, err := dao_ticket.BatchGetPondTicketInfo(ctx, playerId, pondIds)
	if err != nil {
		return nil, err
	}
	list := make([]*commonPB.TicketInfo, 0, len(pondIds))
	for _, pondId := range pondIds {
		info := m[pondId]
		if info == nil {
			info = &dao_ticket.PondTicketRdsInfo{}
		}
		list = append(list, &commonPB.TicketInfo{PondId: pondId, EntryAt: info.EntryAt, Duration: info.DurationSec})
	}
	return list, nil
}

// UseTicket 使用门票，按单张时长叠加到指定钓场，返回最新门票信息
// 参数：itemId 门票道具ID；count 使用数量；pondId 钓场ID；durationPerTicket 每张时长（秒），由上层根据配置传入
func UseTicket(ctx context.Context, playerId uint64, itemId int64, count int32, pondId int64, durationPerTicket int64) (*commonPB.TicketInfo, error) {
	entry := logx.NewLogEntry(ctx)
	if playerId == 0 || itemId <= 0 || count <= 0 || pondId <= 0 || durationPerTicket <= 0 {
		return nil, protox.CodeError(commonPB.ErrCode_ERR_BAD_PARAM, "param error")
	}

	// 这里仅做叠加缓存，不做扣道具，由调用方先行扣除道具
	add := int64(count) * durationPerTicket
	newDur, entryAt, err := dao_ticket.AddOrSetPondTicketTTLSeconds(ctx, playerId, pondId, add)
	if err != nil {
		entry.Errorf("use ticket add ttl error: player=%d pond=%d item=%d count=%d err=%+v", playerId, pondId, itemId, count, err)
		return nil, err
	}

	// 返回最新门票信息
	return &commonPB.TicketInfo{
		PondId:   pondId,
		EntryAt:  entryAt,
		Duration: newDur,
	}, nil
}
