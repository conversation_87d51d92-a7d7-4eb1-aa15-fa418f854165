package logic_ticket

import (
	"context"
	"fmt"
	dao_ticket "hallsrv/internal/dao/dao_ticket"
	logicItem "hallsrv/internal/logic/logic_item"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
)

// GetCurPondTicket 查询多个钓场的门票信息
func GetCurPondTicket(ctx context.Context, playerId uint64, pondIds []int64) ([]*commonPB.TicketInfo, error) {
	if playerId == 0 || len(pondIds) == 0 {
		return nil, protox.CodeError(commonPB.ErrCode_ERR_BAD_PARAM, "param error")
	}
	m, err := dao_ticket.BatchGetPondTicketInfo(ctx, playerId, pondIds)
	if err != nil {
		return nil, err
	}

	list := make([]*commonPB.TicketInfo, 0, len(pondIds))
	for _, pondId := range pondIds {
		info := m[pondId]
		if info == nil {
			info = &dao_ticket.PondTicketRdsInfo{}
		}
		list = append(list, &commonPB.TicketInfo{PondId: pondId, EntryAt: info.EntryAt, Duration: info.DurationSec})
	}

	return list, nil
}

// UseTicket 使用门票，按单张时长叠加到指定钓场，返回最新门票信息
// 参数：itemId 门票道具ID；count 使用数量；pondId 钓场ID；durationPerTicket 每张时长（秒），由上层根据配置传入
func UseTicket(ctx context.Context, playerId uint64, itemId int64, count int32, pondId int64, durationPerTicket int64) (*commonPB.TicketInfo, error) {
	entry := logx.NewLogEntry(ctx)
	if playerId == 0 || itemId <= 0 || count <= 0 || pondId <= 0 || durationPerTicket <= 0 {
		return nil, protox.CodeError(commonPB.ErrCode_ERR_BAD_PARAM, "param error")
	}

	// 这里仅做叠加缓存，不做扣道具，由调用方先行扣除道具
	add := int64(count) * durationPerTicket
	newDur, entryAt, err := dao_ticket.AddOrSetPondTicketTTLSeconds(ctx, playerId, pondId, add)
	if err != nil {
		entry.Errorf("use ticket add ttl error: player=%d pond=%d item=%d count=%d err=%+v", playerId, pondId, itemId, count, err)
		return nil, err
	}

	// 返回最新门票信息
	return &commonPB.TicketInfo{
		PondId:   pondId,
		EntryAt:  entryAt,
		Duration: newDur,
	}, nil
}

// UseTicketWithItemReduce 使用门票叠加时长（包含道具扣除）
// 参数：playerId 玩家ID；itemId 门票道具ID；count 使用数量；pondId 钓场ID
// 返回：门票信息和错误
func UseTicketWithItemReduce(ctx context.Context, playerId uint64, itemId int64, count int32, pondId int64) (*commonPB.TicketInfo, error) {
	entry := logx.NewLogEntry(ctx)

	// 参数校验
	if playerId == 0 || itemId <= 0 || count <= 0 || pondId <= 0 {
		return nil, protox.CodeError(commonPB.ErrCode_ERR_BAD_PARAM, "param error")
	}

	// 从配置中读取门票每张时长（秒）
	durationPerTicket, err := getTicketDurationFromConfig(ctx, itemId)
	if err != nil {
		entry.Errorf("get ticket duration config error: itemId=%d err=%+v", itemId, err)
		return nil, err
	}

	if durationPerTicket <= 0 {
		return nil, protox.CodeError(commonPB.ErrCode_ERR_BAD_PARAM, "ticket duration config missing")
	}

	// 扣道具
	cost := []*commonPB.ItemBase{{ItemId: itemId, ItemCount: int64(count)}}
	_, err = logicItem.OperatePlayerItem(ctx, playerId, cost, commonPB.ITEM_OPERATION_IO_REDUCE, commonPB.ITEM_SOURCE_TYPE_IST_STORE_BUY, commonPB.STORAGE_TYPE_ST_STORE, false)
	if err != nil {
		entry.Errorf("UseTicket reduce item error: player=%d itemId=%d count=%d err=%+v", playerId, itemId, count, err)
		return nil, err
	}

	// 使用门票叠加时长
	info, err := UseTicket(ctx, playerId, itemId, count, pondId, durationPerTicket)
	if err != nil {
		entry.Errorf("UseTicket add ttl error: player=%d itemId=%d count=%d pondId=%d err=%+v", playerId, itemId, count, pondId, err)
		return nil, err
	}

	return info, nil
}

// getTicketDurationFromConfig 从配置中获取门票每张时长
// TODO: 这里需要根据实际的配置结构来实现，目前返回固定值作为示例
func getTicketDurationFromConfig(ctx context.Context, itemId int64) (int64, error) {
	// 获取道具配置
	itemConf := cmodel.GetItem(itemId, consul_config.WithGrpcCtx(ctx))
	if itemConf == nil {
		return 0, protox.CodeError(commonPB.ErrCode_ERR_CONF_ERROR, fmt.Sprintf("item config not found: itemId=%d", itemId))
	}

	// TODO: 根据实际配置结构获取门票时长
	// 这里需要根据实际的配置表结构来实现
	// 示例：假设在道具配置的某个字段中存储了门票时长
	// 或者需要单独的门票配置表来映射 itemId -> duration

	// 临时实现：根据道具类型或ID返回固定时长
	// 实际项目中应该从配置表中读取
	switch itemId {
	case 10001: // 示例：1小时门票
		return 3600, nil
	case 10002: // 示例：3小时门票
		return 10800, nil
	case 10003: // 示例：24小时门票
		return 86400, nil
	default:
		// 如果没有配置，返回错误
		return 0, protox.CodeError(commonPB.ErrCode_ERR_CONF_ERROR, fmt.Sprintf("ticket duration not configured for itemId=%d", itemId))
	}
}
