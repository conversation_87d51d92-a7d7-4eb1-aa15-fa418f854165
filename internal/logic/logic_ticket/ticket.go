package logic_ticket

import (
	"context"
	"time"
	"hallsrv/internal/dao/dao_ticket"
	logicItem "hallsrv/internal/logic/logic_item"
	"hallsrv/internal/model/model_ticket"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
)

// GetCurPondTicket 查询多个钓场的门票信息
func GetCurPondTicket(ctx context.Context, playerId uint64, pondIds []int64) ([]*commonPB.TicketInfo, error) {
	if playerId == 0 || len(pondIds) == 0 {
		return nil, protox.CodeError(commonPB.ErrCode_ERR_BAD_PARAM, "param error")
	}
	m, err := dao_ticket.BatchGetPondTicketInfo(ctx, playerId, pondIds)
	if err != nil {
		return nil, err
	}

	list := make([]*commonPB.TicketInfo, 0, len(pondIds))
	for _, pondId := range pondIds {
		info := m[pondId]
		if info == nil {
			info = &model_ticket.PondTicketInfo{}
		}
		list = append(list, &commonPB.TicketInfo{PondId: pondId, EntryAt: info.EntryAt, Duration: info.DurationSec})
	}

	return list, nil
}

// UseTicketWithItemReduce 使用门票叠加时长（包含道具扣除）
// 参数：playerId 玩家ID；itemId 门票道具ID；count 使用数量；pondId 钓场ID
// 返回：门票信息和错误
func UseTicketWithItemReduce(ctx context.Context, playerId uint64, itemId int64, count int32, pondId int64) (*commonPB.TicketInfo, error) {
	entry := logx.NewLogEntry(ctx)

	// 参数校验
	if playerId == 0 || itemId <= 0 || count <= 0 || pondId <= 0 {
		return nil, protox.CodeError(commonPB.ErrCode_ERR_BAD_PARAM, "param error")
	}

	fishPondCfg := cmodel.GetFishPondList(pondId, consul_config.WithGrpcCtx(ctx))
	if fishPondCfg == nil {
		return nil, protox.CodeError(commonPB.ErrCode_ERR_CONF_ERROR, "pond config not found")
	}

	// 校验
	for _, ticket := range fishPondCfg.NewEntryItem {
		if ticket == itemId {
			break
		}
		return nil, protox.CodeError(commonPB.ErrCode_ERR_BAD_PARAM, "item not match pond")
	}

	// 扣道具
	cost := []*commonPB.ItemBase{{ItemId: itemId, ItemCount: int64(count)}}
	_, err := logicItem.OperatePlayerItem(ctx, playerId, cost, commonPB.ITEM_OPERATION_IO_REDUCE,
		commonPB.ITEM_SOURCE_TYPE_IST_ENTRY_POND_FEE, commonPB.STORAGE_TYPE_ST_STORE, false)
	if err != nil {
		entry.Errorf("UseTicket reduce item error: player=%d itemId=%d count=%d err=%+v", playerId, itemId, count, err)
		return nil, err
	}

	// 获取票券时间
	itemTicketCfg := cmodel.GetItemTicket(itemId, consul_config.WithGrpcCtx(ctx))
	if itemTicketCfg == nil {
		return nil, protox.CodeError(commonPB.ErrCode_ERR_CONF_ERROR, "item ticket config not found")
	}

	// 使用门票叠加时长
	info, err := UseTicket(ctx, playerId, itemId, count, pondId, itemTicketCfg.Duration)
	if err != nil {
		entry.Errorf("UseTicket add ttl error: player=%d itemId=%d count=%d pondId=%d err=%+v", playerId, itemId, count, pondId, err)
		return nil, err
	}

	return info, nil
}

// UseTicket 使用门票，按单张时长叠加到指定钓场，返回最新门票信息
// 参数：itemId 门票道具ID；count 使用数量；pondId 钓场ID；durationPerTicket 每张时长（秒）
func UseTicket(ctx context.Context, playerId uint64, itemId int64, count int32, pondId int64, durationPerTicket int64) (*commonPB.TicketInfo, error) {
	entry := logx.NewLogEntry(ctx)
	if playerId == 0 || itemId <= 0 || count <= 0 || pondId <= 0 || durationPerTicket <= 0 {
		return nil, protox.CodeError(commonPB.ErrCode_ERR_BAD_PARAM, "param error")
	}

	// 获取当前门票信息
	currentInfo, err := dao_ticket.GetPondTicketInfo(ctx, playerId, pondId)
	if err != nil {
		entry.Errorf("get current ticket info error: player=%d pond=%d err=%+v", playerId, pondId, err)
		return nil, err
	}

	// 计算新的时长
	addSeconds := int64(count) * durationPerTicket
	newDuration := currentInfo.DurationSec + addSeconds
	limitTime := int64(1000 * 3600) // 1000小时限制
	if newDuration > limitTime {
		newDuration = limitTime
	}

	// 确定 entry_at（首次使用时间）
	entryAt := time.Now().Unix()
	if currentInfo.EntryAt > 0 {
		entryAt = currentInfo.EntryAt // 保持首次使用时间不变
	}

	// 构建新的门票信息
	newTicketInfo := &model_ticket.PondTicketInfo{
		PondId:      pondId,
		EntryAt:     entryAt,
		DurationSec: newDuration,
	}

	// 保存到Redis
	updatedInfo, err := dao_ticket.SetPondTicketInfo(ctx, playerId, newTicketInfo)
	if err != nil {
		entry.Errorf("set ticket info error: player=%d pond=%d err=%+v", playerId, pondId, err)
		return nil, err
	}

	// 返回最新门票信息
	return &commonPB.TicketInfo{
		PondId:   updatedInfo.PondId,
		EntryAt:  updatedInfo.EntryAt,
		Duration: updatedInfo.DurationSec,
	}, nil
}
