package logicGuide

import (
	"context"

	rpcUser "hallsrv/internal/repo/rpc_user"

	logicItem "hallsrv/internal/logic/logic_item"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	interceptor "git.keepfancy.xyz/back-end/frameworks/kit/rpc/interceptor"
)

// UpdateGuideProgress 更新玩家新手引导进度
func UpdateGuideProgress(ctx context.Context, playerId uint64, progress int32) error {
	entry := logx.NewLogEntry(ctx)
	if progress < 0 {
		entry.Errorf("progress is less than 0")
		return protox.CodeError(commonPB.ErrCode_ERR_BAD_PARAM, "progress is less than 0")
	}

	// 更新玩家新手引导进度 rpc到user服
	opts := interceptor.GetRPCOptions(ctx)
	productId := opts.ProductId

	// 查询玩家当前新手引导进度
	extendInfo, err := rpcUser.RpcQueryPlayerExtendInfo(ctx, productId, playerId)
	if err != nil {
		entry.Errorf("rpc query player:%d guide progress failed : %v", playerId, err)
		return err
	}

	curProcess := extendInfo.GetNoviceGuide()

	// 判断是否需要更新
	if progress <= curProcess {
		entry.Warnf("player:%d guide progress:%d is less than current progress:%d", playerId, progress, curProcess)
		return nil
	}

	err = rpcUser.RpcUpdateGuideProgress(ctx, productId, playerId, progress)
	if err != nil {
		entry.Errorf("rpc update player:%d guide progress failed : %v", playerId, err)
		return err
	}

	// 查询配置 有奖励且 mod == 1 发奖
	noviceConf := cmodel.GetGameNoviceGuide(int64(progress), consul_config.WithGrpcCtx(ctx))
	if noviceConf != nil && noviceConf.GiftId > 0 && noviceConf.Mode == 1 {
		awardList := make([]*commonPB.ItemBase, 0)
		awardList = append(awardList, &commonPB.ItemBase{
			ItemId:    noviceConf.GiftId,
			ItemCount: 1,
		})

		// 发奖
		logicItem.OperatePlayerItem(ctx, playerId, awardList, commonPB.ITEM_OPERATION_IO_ADD, commonPB.ITEM_SOURCE_TYPE_IST_GUIDE_REWARD, commonPB.STORAGE_TYPE_ST_STORE, true)
		entry.Infof("player:%d guide progress:%d has reward", playerId, progress)
	}

	entry.Debugf("player:%d guide progress:%d success", playerId, progress)

	return nil
}
