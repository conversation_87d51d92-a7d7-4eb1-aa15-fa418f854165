package logic_support

import (
	"context"
	"hallsrv/internal/repo/real_name"
	"hallsrv/internal/repo/real_name/real_name_def"
	rpcUser "hallsrv/internal/repo/rpc_user"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	hallPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/hall"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/frameworks/dict"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	"git.keepfancy.xyz/back-end/frameworks/kit/rpc/interceptor"
	"git.keepfancy.xyz/back-end/frameworks/lib/ddm"
	"git.keepfancy.xyz/back-end/frameworks/lib/transform"
	"github.com/sirupsen/logrus"
)

// VerifyRealName 实名认证
func VerifyRealName(ctx context.Context, req *hallPB.RealNameAuthReq) *hallPB.RealNameAuthRsp {
	entry := logx.NewLogEntry(ctx)

	playerId := interceptor.GetRPCOptions(ctx).PlayerId
	productId := interceptor.GetRPCOptions(ctx).ProductId

	logEntry := entry.WithFields(logrus.Fields{
		dict.SysWordPlayerID: playerId,
	})
	logEntry.Tracef("RealNameVerifyReq - :%d - req : %v", playerId, req)

	rsp := &hallPB.RealNameAuthRsp{
		Ret: protox.FillCodeResult(commonPB.ErrCode_ERR_FAIL),
	}

	// 检查配置
	realNameCfg := cmodel.GetRealNameAuth(consul_config.WithGrpcCtx(ctx))
	openAuth := realNameCfg.Enable
	authRuleType := realNameCfg.RuleType

	if !openAuth {
		logEntry.Error("real-name auth not open")
		rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_HALL_REAL_NAME_NOT_OPEN, "Real-name authentication failed")
		return rsp
	}

	realName := req.RealName
	idCardNum := req.IdCardNum

	flag, err := rpcUser.RpcRealNameAuthQuery(ctx, productId, playerId)
	if err != nil {
		rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_DB_OPE_ERROR, err.Error())
		logEntry.WithError(err).Errorf("Real-name authentication failed query fail err:%+v", err)
		return rsp
	}

	// 已经实名认证
	if flag {
		rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_FAIL, "Real-name authentication has been submitted. Please do not submit it again")
		logEntry.Debugf("Real-name authentication is exist, req:%+v, rsp:%+v", req, rsp)
		return rsp
	}

	rnType := real_name.RealNameTypeOffice
	if authRuleType == 2 {
		rnType = real_name.RealNameTypeShuMai
	}

	rnAuthRet, rnError := real_name.DoRealNameAuth(ctx, rnType, playerId, realName, idCardNum)
	if rnError != nil || rnAuthRet.State != real_name_def.AuthRetAgreed {
		logEntry.WithError(rnError).Errorf("Real-name authentication failed status:%+v", rnAuthRet)
		rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_FAIL, "Real-name authentication failed")
		return rsp
	}

	icY, icM, icD := transform.SubYMDFromIdCardNum(idCardNum)

	// isMinor := real_name.IsChildPlayer(info)
	// if isMinor {
	// 	// TODO NTF客户端 未成年人
	// 	// TODO 剩下多少时间
	// } else {
	// 	// TODO 成年人
	// }

	// 对数据加密

	idCard, _ := ddm.IDCard(idCardNum).MarshalJSON()
	idName, _ := ddm.IDName(realName).MarshalJSON()

	info := &commonPB.PlayerRealNameAuth{
		ProductId: productId,
		PlayerId:  playerId,
		Pi:        rnAuthRet.Pi,
		RealName:  string(idName),
		IdCard:    string(idCard),
		Year:      icY,
		Month:     icM,
		Day:       icD,
	}

	err = rpcUser.RpcUpdateRealNameAuth(ctx, info)
	if err != nil {
		logEntry.WithError(err).Errorf("real-name update fail")
		rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_DB_OPE_ERROR, err.Error())
		return rsp
	}

	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)

	logEntry.Debugf("Real-name authentication success, req:%+v, rsp:%+v", req, rsp)

	return rsp
}
