package logic_player_state

import (
	"context"
	modelPlayer "hallsrv/internal/model/model_player"
	test_init "hallsrv/internal/test"
	"testing"
)

func TestQueryPlayerStateBmStr(t *testing.T) {
	test_init.InitRedisConsul()
	test_init.InitSql()

	ctx := context.Background()
	playerId := uint64(1)
	//rdsField := modelPlayer.GetPlayerStateBmExpLvRdsField()
	rdsField := modelPlayer.GetPlayerStateBmGoodsRdsField()
	expectedBmStr := "fffff"

	bmStr, err := QueryPlayerStateBmStr(ctx, playerId, rdsField)
	t.Logf("测试用例 1 获取不到数据  bmStr: %s, err: %v", bmStr, err)

	// 测试用例 2: 修改入库
	err = UpdatePlayerStateBmStr(ctx, playerId, rdsField, expectedBmStr)
	t.Logf("测试用例 1 写入数据: %s, err: %v", expectedBmStr, err)

	bmStr, err = QueryPlayerStateBmStr(ctx, playerId, rdsField)
	t.Logf("测试用例 1 获取数据  bmStr: %s, err: %v", bmStr, err)

}
