package logic_player_state

import (
	"context"
	"fmt"
	"hallsrv/internal/dao/dao_player"
	"time"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"github.com/spf13/cast"
)

// QueryPlayerStateBmStr 查询玩家指定字段的bitmap信息
func QueryPlayerStateBmStr(ctx context.Context, playerId uint64, rdsField string) (string, error) {
	entry := logx.NewLogEntry(ctx)
	if playerId == 0 || rdsField == "" {
		entry.Warnf("get state bm error, player:%d or rdsField:%s zero", playerId, rdsField)
		return "", fmt.Errorf("player or field error")
	}

	// 查询玩家redis bitmap信息
	psRds, err := daoPlayer.QueryPlayerStateBmStrRds(ctx, playerId)
	if err != nil {
		entry.Warnf("get player:%d state bm error info field:%s error, %s", playerId, rdsField, err)
	}

	// 如果redis存在
	if psRds != nil {
		if val, ok := psRds[rdsField]; ok {
			return val, nil
		}
	}

	// 如果redis不存在 查询数据库
	psRdb, err := daoPlayer.QueryPlayerStateBmStrRdb(ctx, playerId)
	if err != nil {
		entry.Errorf("get player:%d state bm by db error info field:%s error, %s", playerId, rdsField, err)
		return "", err
	}

	if psRdb == nil {
		return "", fmt.Errorf("player:%d state bm rdb not exist", playerId)
	}

	// 转成map
	psMap := psRdb.ToRedisHashField()
	if psMap == nil {
		return "", fmt.Errorf("player:%d state bm toRedisHash error ", playerId)
	}

	// 写入redis
	daoPlayer.CreatePlayerStateBmStrRds(ctx, playerId, psMap)

	entry.Debugf("get player:%d state bm info field:%s, %s", playerId, rdsField, psMap[rdsField])

	return cast.ToString(psMap[rdsField]), nil
}

// UpdatePlayerStateBmStr 更新玩家指定字段的bitmap信息
func UpdatePlayerStateBmStr(ctx context.Context, playerId uint64, rdsField string, bmStr string) error {
	entry := logx.NewLogEntry(ctx)
	if playerId == 0 || rdsField == "" || bmStr == "" {
		entry.Warnf("update state bm error, player:%d or rdsField:%s zero", playerId, rdsField)
		return fmt.Errorf("player or field error")
	}

	// 删除redis中的玩家信息
	err := daoPlayer.DeletePlayerStateBmStrRds(ctx, playerId)
	if err != nil {
		entry.Warnf("delete player:%d state bm error info field:%s, %s", playerId, rdsField, err)
		return err
	}

	// 延迟双删
	time.AfterFunc(3*time.Second, func() {
		if err := daoPlayer.DeletePlayerStateBmStrRds(context.Background(), playerId); err != nil {
			entry.Warnf("double delete player:%d state bm error info field:%s, %s", playerId, rdsField, err)
		}
	})

	// 更新到数据库
	err = daoPlayer.InsertOrUpdatePlayerStateBmStrRdb(ctx, playerId, rdsField, bmStr)
	if err != nil {
		entry.Errorf("update player:%d state bm error info field:%s, %s", playerId, rdsField, err)
		return err
	}

	entry.Debugf("update player:%d state bm info field:%s, %s", playerId, rdsField, bmStr)

	return err
}
