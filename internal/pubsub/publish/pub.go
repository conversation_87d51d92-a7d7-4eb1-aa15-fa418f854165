package publish

import (
	"context"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/mq"
	"git.keepfancy.xyz/back-end/frameworks/kit/rpc/interceptor"
	"google.golang.org/protobuf/proto"
	modelItem "hallsrv/internal/model/model_item"
)

// PublishItemChange 推送道具变更事件
func PublishItemChange(ctx context.Context, playerId uint64, changeType commonPB.ITEM_OPERATION, itemList []*modelItem.ItemOptParam, source commonPB.ITEM_SOURCE_TYPE) {
	newItemList := make([]*commonPB.ItemInfo, len(itemList))
	for i, item := range itemList {
		newItemList[i] = &commonPB.ItemInfo{
			Item: &commonPB.Item{
				ItemId:       item.ItemId,
				ItemType:     commonPB.ITEM_TYPE(item.ItemType),
				ItemCategory: commonPB.ITEM_CATEGORY(item.CategoryType),
				ItemSubType:  item.ItemSubType,
			},
			ItemDeltaCount: item.ItemNum,
		}
	}

	switch changeType {
	case commonPB.ITEM_OPERATION_IO_ADD:
		publishItemAdd(ctx, playerId, newItemList, source)
	case commonPB.ITEM_OPERATION_IO_REDUCE:
		publishItemReduce(ctx, playerId, newItemList, source)
	}
}

// 道具增加事件
func publishItemAdd(ctx context.Context, playerId uint64, itemList []*commonPB.ItemInfo, source commonPB.ITEM_SOURCE_TYPE) {
	for _, item := range itemList {
		opts := interceptor.GetRPCOptions(ctx)
		eventData := packItem(item, source)
		data := &commonPB.EventCommon{
			PlayerId:  playerId,
			ProductId: opts.ProductId,
			ChannelId: int32(opts.ChannelType),
			EventType: commonPB.EVENT_TYPE_ET_ITEM_ADD,
			IntData:   eventData,
		}

		js, _ := proto.Marshal(data)
		err := mq.PublishEvent(commonPB.EVENT_TYPE_ET_ITEM_ADD.String(), js)
		if err != nil {
			logx.NewLogEntry(ctx).Errorf("push event [%+v] fail:%+v", data, err)
		}
	}
}

// 道具消耗事件
func publishItemReduce(ctx context.Context, playerId uint64, itemList []*commonPB.ItemInfo, source commonPB.ITEM_SOURCE_TYPE) {
	for _, item := range itemList {
		opts := interceptor.GetRPCOptions(ctx)
		eventData := packItem(item, source)
		data := &commonPB.EventCommon{
			PlayerId:  playerId,
			ProductId: opts.ProductId,
			ChannelId: int32(opts.ChannelType),
			EventType: commonPB.EVENT_TYPE_ET_ITEM_REDUCE,
			IntData:   eventData,
		}

		js, _ := proto.Marshal(data)
		err := mq.PublishEvent(commonPB.EVENT_TYPE_ET_ITEM_REDUCE.String(), js)
		if err != nil {
			logx.NewLogEntry(ctx).Errorf("push event [%+v] fail:%+v", data, err)
		}
	}
}

// 打包item
func packItem(item *commonPB.ItemInfo, source commonPB.ITEM_SOURCE_TYPE) map[int32]int64 {
	eventData := make(map[int32]int64)
	eventData[int32(commonPB.EVENT_INT_KEY_EIK_ITEM_SOURCE)] = int64(source)
	eventData[int32(commonPB.EVENT_INT_KEY_EIK_ITEM_ID)] = int64(item.Item.ItemId)
	eventData[int32(commonPB.EVENT_INT_KEY_EIK_ITEM_CATEGORY)] = int64(item.Item.ItemCategory)
	eventData[int32(commonPB.EVENT_INT_KEY_EIK_ITEM_TYPE)] = int64(item.Item.ItemType)
	eventData[int32(commonPB.EVENT_INT_KEY_EIK_ITEM_COUNT)] = int64(item.ItemDeltaCount)

	return eventData
}

func PublishLevChange(ctx context.Context, playerId uint64, lev int64, beforeLev int64) {
	opts := interceptor.GetRPCOptions(ctx)
	eventData := make(map[int32]int64)
	eventData[int32(commonPB.EVENT_INT_KEY_EIK_ROLE_LEVEL)] = int64(lev)
	eventData[int32(commonPB.EVENT_INT_KEY_EIK_ROLE_BEFORE_LEVEL)] = int64(beforeLev)
	typ := commonPB.EVENT_TYPE_ET_ROLE_LEVEL_UP
	data := &commonPB.EventCommon{
		PlayerId:  playerId,
		ProductId: opts.ProductId,
		ChannelId: int32(opts.ChannelType),
		EventType: typ,
		IntData:   eventData,
	}

	js, _ := proto.Marshal(data)
	err := mq.PublishEvent(typ.String(), js)
	if err != nil {
		logx.NewLogEntry(ctx).Errorf("push event [%+v] fail:%+v", data, err)
	}
}
