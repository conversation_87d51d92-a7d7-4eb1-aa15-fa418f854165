package config

import (
	"fmt"
	"time"
)

// redisKey
const (
	// -----------------  string  -------------------
	// 假日信息(全局变量)
	RDS_KEY_HOLIDAY_INFO = "holiday_info"
	// 玩家月充值总金额(分)p_month_recharge:{month}:{playerId}
	RDS_KEY_MONTH_RECHARGE = "p_month_recharge:%s:%d"
	// CDK日志
	RDS_KEY_CDK_LOG = "cdk_log:%s"
	// CDK批次
	RDS_KEY_CDK_BATCH = "cdk_batch:%d"
	// CDK信息缓存 cdk_info:{cdkCode}
	RDS_KEY_CDK_INFO = "cdk_info:%s"

	// -----------------  hash  -------------------
	// 旅途背包 tbag:{playerId}:{bagType}
	RDS_KEY_TRIP_BAG = "tbag:%d:%d"
	// 旅途钓组 trod:{playerId}
	RDS_KEY_TRIP_ROD = "trod:%d"
	// 玩家bitmap 数据
	RDS_KEY_PLAYER_BITMAP = "p_bitmap:%d"
	// 玩家商品购买次数信息
	RDS_KEY_PLAYER_GOODS_BUY = "goods_buy:%d"
	// 上次游戏信息
	RDS_KEY_LAST_GAME_INFO = "p_last_game:%d"
	// 竿架系统
	RDS_KEY_ROD_RIG_INFO = "rig_info:%d"
	// 玩家道具堆耐久百分比item_heap_durable:{playerId}
	RDS_KEY_ITEM_HEAP_DURABLE = "item_heap_durable:%d"
	// 道具使用冷却时间 item_cd:{playerId}
	RDS_KEY_ITEM_COOLDOWN = "item_cd:%d"
	// 局内商城 pond_store:{playerId}:{pondId}
	RDS_KEY_POND_STORE = "pond_store:%d:%d"
	// 局内商城商品购买次数 goods_buy_count:{channel}:{pondId}:
	RDS_KEY_POND_STORE_GOODS = "pond_store_goods:%d:%d"
	// 门票缓存 tk_pond:{playerId}:{pondId}
	RDS_KEY_POND_TICKET = "tk_pond:%d:%d"

	// -----------------  zset  -------------------

	// -----------------  singleflight  -------------------
	// 玩家状态锁
	RDS_CREATE_PLAYER_STATE_BM_SINGLEFLIGHT = "sf:playerState:bm:%d"

	// -----------------  activity 活动  -------------------
	// 连续登录 activity:cl:{playerId}
	RDS_KEY_CONTINUOUS_LOGIN = "act:cl:%d"
)

// -----------------  lock  -------------------
const (
	// 竿架系统锁key
	RDS_KEY_ROD_RIG_LOCK = "lock:rod_rig:%d"

	// 杆包系统锁
	RDS_KEY_ROD_BAG_LOCK = "lock:bag_rod:%d"

	// CDK兑换锁
	RDS_KEY_CDK_LOCK = "lock:cdk:%s"

	// 钓场商城用户锁 key
	RDS_KEY_POND_STORE_PLAYER_LOCK = "lock:pond_store:player:%d"

	// 钓场商城锁 key
	RDS_KEY_POND_STORE_LOCK = "lock:pond_store"
)

// redisExpire
const (
	// 旅途背包 1小时
	TRIP_BAG_EXPIRE = 3600 * time.Second
	// 旅途钓组 1小时
	TRIP_ROD_EXPIRE = 3600 * time.Second
	// 玩家商品购买次数信息 30天
	PLAYER_GOODS_BUY_EXPIRE = 30 * 24 * time.Hour
	// 上次游戏信息 7 天
	LAST_GAME_INFO_EXPIRE = 7 * 24 * time.Hour
	// 竿架系统 7 天
	ROD_RIG_INFO_EXPIRE = 7 * 24 * time.Hour
	// 玩家状态 7天
	PLAYER_STATE_BITMAP_EXPIRE = 7 * 24 * time.Hour
	// 玩家道具堆耐久百分比 7天
	ITEM_HEAP_DURABLE_EXPIRE = 7 * 24 * time.Hour
	// 道具冷却时间过期时间 2天
	ITEM_COOLDOWN_EXPIRE = 2 * 24 * time.Hour
	// 玩家月充值金额过期时间 1个月
	MONTH_RECHARGE_EXPIRE = 30 * 24 * time.Hour
	// CDK缓存过期时间 1天
	CDK_CACHE_EXPIRE = 24 * time.Hour
	// 门票最大累计时长（秒）1000小时
	TICKET_MAX_SECONDS = 1000 * 3600
)

func CreatePlayerStateBmSingleflight(playerId uint64) string {
	return fmt.Sprintf(RDS_CREATE_PLAYER_STATE_BM_SINGLEFLIGHT, playerId)
}

func GetPlayerBitmapRdsKey(playerId uint64) string {
	return fmt.Sprintf(RDS_KEY_PLAYER_BITMAP, playerId)
}

func CdkLockKey(cdkCode string) string {
	return fmt.Sprintf(RDS_KEY_CDK_LOCK, cdkCode)
}

func CdkInfoKey(cdkCode string) string {
	return fmt.Sprintf(RDS_KEY_CDK_INFO, cdkCode)
}

// ItemCooldownKey 道具冷却Redis key
func ItemCooldownKey(playerId uint64) string {
	return fmt.Sprintf(RDS_KEY_ITEM_COOLDOWN, playerId)
}

// GetRdsLockPondStore 获取局内商城锁redisKey
func GetRdsLockPondStore() string {
	return RDS_KEY_POND_STORE_LOCK
}

// GetRdsLockPondStore 获取局内商城用户缓存锁redisKey
func GetRdsLockPlayerPondStore(playerId uint64) string {
	return fmt.Sprintf(RDS_KEY_POND_STORE_PLAYER_LOCK, playerId)
}

// GetRdsKeyPlayerPondStore 获取局内商城Player reidsKey
func GetRdsKeyPlayerPondStore(playerId uint64, pondId int64) string {
	return fmt.Sprintf(RDS_KEY_POND_STORE, playerId, pondId)
}

// GetRdsKeyPondStoreGoods 获取局内商城商品购买次数redisKey
func GetRdsKeyPondStoreGoods(channel int32, pondId int64) string {
	return fmt.Sprintf(RDS_KEY_POND_STORE_GOODS, channel, pondId)
}

// GetRdsKeyPondTicket 获取玩家钓场门票缓存key
func GetRdsKeyPondTicket(playerId uint64, pondId int64) string {
	return fmt.Sprintf(RDS_KEY_POND_TICKET, playerId, pondId)
}
