package config

import (
	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/config"
)

// InitConfig 初始化配置
func InitConfig() error {
	serviceConfig := config.NewServiceConfig()

	serviceConfig.Register("InitFishPondListCfg", cmodel.InitFishPondListCfg)         // 钓场配置
	serviceConfig.Register("InitGoodsBasicCfg", cmodel.InitGoodsBasicCfg)             // 商品配置
	serviceConfig.Register("InitStoreBuyCfg", cmodel.InitStoreBuyCfg)                 // 商城购买配置
	serviceConfig.Register("InitItemCfg", cmodel.InitItemCfg)                         // 道具配置
	serviceConfig.Register("InitItemConstCfg", cmodel.InitItemConstCfg)               // 道具静态配置
	serviceConfig.Register("InitItemFoodCfg", cmodel.InitItemFoodCfg)                 // 食品道具配置
	serviceConfig.Register("InitGiftBasicCfg", cmodel.InitGiftBasicCfg)               // 礼包配置
	serviceConfig.Register("InitRoleLevelCfg", cmodel.InitRoleLevelCfg)               // 等级经验配置
	serviceConfig.Register("InitRodsCfg", cmodel.InitRodsCfg)                         // 竿配置
	serviceConfig.Register("InitLinesCfg", cmodel.InitLinesCfg)                       // 线配置
	serviceConfig.Register("InitReelsCfg", cmodel.InitReelsCfg)                       // 轮配置
	serviceConfig.Register("InitRigRuleCfg", cmodel.InitRigRuleCfg)                   // 钓组规则配置
	serviceConfig.Register("InitTripBagStoreRuleCfg", cmodel.InitTripBagStoreRuleCfg) // 背包存储规则
	serviceConfig.Register("InitStoreRoomCfg", cmodel.InitStoreRoomCfg)               // 房间商品配置
	serviceConfig.Register("InitPurchaseListCfg", cmodel.InitPurchaseListCfg)         // 上次购买配置
	serviceConfig.Register("InitRealNameAuthCfg", cmodel.InitRealNameAuthCfg)         // 实名认证配置
	serviceConfig.Register("InitGameNoviceGuideCfg", cmodel.InitGameNoviceGuideCfg)   // 新手引导配置
	serviceConfig.Register("InitContinuousLoginCfg", cmodel.InitContinuousLoginCfg)   // 连续登录配置
	serviceConfig.Register("InitRechargeLimitCfg", cmodel.InitRechargeLimitCfg)       // 充值限制配置
	serviceConfig.Register("InitBobbersCfg", cmodel.InitBobbersCfg)                   // 浮漂配置
	serviceConfig.Register("InitItemCdCfg", cmodel.InitItemCdCfg)                     // 道具cd配置
	serviceConfig.Register("InitPondStoreCfg", cmodel.InitPondStoreCfg)               // 钓场商品配置
	serviceConfig.Register("InitPondStoreGroupCfg", cmodel.InitPondStoreGroupCfg)     // 钓场商品配置

	return serviceConfig.ExecuteAll()
}
