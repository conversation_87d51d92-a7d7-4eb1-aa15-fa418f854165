package main

import (
	"context"
	"hallsrv/internal/config"
	"hallsrv/internal/repo/record"
	"runtime"

	logicRig "hallsrv/internal/logic/logic_rig"
	"hallsrv/internal/proc"
	"hallsrv/internal/server/rpc"

	"git.keepfancy.xyz/back-end/frameworks/dict"
	"git.keepfancy.xyz/back-end/frameworks/kit/driver"
	"git.keepfancy.xyz/back-end/frameworks/kit/forbid"
	"git.keepfancy.xyz/back-end/frameworks/lib/random"
	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
)

type hallService struct {
	Name string
	Ctx  context.Context
}

func (h *hallService) Init() error {
	h.Ctx = context.Background()
	h.Name = viper.GetString(dict.ConfigRpcServerName)
	logrus.Infoln(h.Name + "服务Init")
	
	// 初始化配置
	err := config.InitConfig()
	if err != nil {
		logrus.Errorf("初始化配置失败:%+v", err)
		return err
	}

	// 注册客户端消息
	proc.RegClientMsgHandler()

	// 注册GM接口
	proc.InitGmRpc()

	// 初始化rpc接口
	rpc.InitHallRpc()

	// 初始化钓组规则
	logicRig.LoadRigRuleCache()

	// 初始化流水
	record.Init()

	// 屏蔽字库
	forbid.InitForbid()

	// 实例化日志流水作业
	record.DefaultLogging = record.NewHallRecordWorkPool()
	record.DefaultLogging.StartItemRecordWorkerPool()

	return nil
}

func (h *hallService) Start() error {
	return nil
}

func (h *hallService) Stop() error {
	return nil
}

func (h *hallService) ForceStop() error {
	return nil
}

func main() {
	runtime.GOMAXPROCS(runtime.NumCPU() * 2)
	random.NewInitSource()
	driver.Run(&hallService{})
}
